using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Parameters;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 设备参数实体配置
/// </summary>
public class DeviceParameterConfiguration : IEntityTypeConfiguration<DeviceParameter>
{
    public void Configure(EntityTypeBuilder<DeviceParameter> builder)
    {
        builder.ToTable("DeviceParameters");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.CustomName)
            .HasMaxLength(100)
            .HasComment("自定义名称");
            
        builder.Property(x => x.CustomConfiguration)
            .HasColumnType("nvarchar(max)")
            .HasComment("自定义配置JSON");
        
        // 索引
        builder.HasIndex(x => new { x.DeviceId, x.ParameterDefinitionId })
            .IsUnique()
            .HasDatabaseName("IX_DeviceParameters_Device_Parameter");
            
        builder.HasIndex(x => x.IsEnabled)
            .HasDatabaseName("IX_DeviceParameters_IsEnabled");
        
        // 关系配置
        builder.HasOne(x => x.Device)
            .WithMany(x => x.DeviceParameters)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasOne(x => x.ParameterDefinition)
            .WithMany(x => x.DeviceParameters)
            .HasForeignKey(x => x.ParameterDefinitionId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.MonitoringData)
            .WithOne(x => x.DeviceParameter)
            .HasForeignKey(x => x.DeviceParameterId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
