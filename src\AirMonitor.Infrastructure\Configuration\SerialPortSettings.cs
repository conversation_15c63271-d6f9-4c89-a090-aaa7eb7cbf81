using System.IO.Ports;

namespace AirMonitor.Infrastructure.Configuration;

/// <summary>
/// 串口通信配置设置
/// </summary>
public class SerialPortSettings
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "SerialPort";

    /// <summary>
    /// 默认串口名称
    /// </summary>
    public string DefaultPortName { get; set; } = "COM1";

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; } = 9600;

    /// <summary>
    /// 数据位
    /// </summary>
    public int DataBits { get; set; } = 8;

    /// <summary>
    /// 停止位
    /// </summary>
    public StopBits StopBits { get; set; } = StopBits.One;

    /// <summary>
    /// 奇偶校验
    /// </summary>
    public Parity Parity { get; set; } = Parity.None;

    /// <summary>
    /// 读取超时时间（毫秒）
    /// </summary>
    public int ReadTimeout { get; set; } = 5000;

    /// <summary>
    /// 写入超时时间（毫秒）
    /// </summary>
    public int WriteTimeout { get; set; } = 5000;

    /// <summary>
    /// 数据接收缓冲区大小
    /// </summary>
    public int ReceiveBufferSize { get; set; } = 4096;

    /// <summary>
    /// 数据发送缓冲区大小
    /// </summary>
    public int SendBufferSize { get; set; } = 4096;

    /// <summary>
    /// 是否启用 DTR（数据终端就绪）
    /// </summary>
    public bool DtrEnable { get; set; } = false;

    /// <summary>
    /// 是否启用 RTS（请求发送）
    /// </summary>
    public bool RtsEnable { get; set; } = false;

    /// <summary>
    /// 连接重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 连接重试间隔（毫秒）
    /// </summary>
    public int RetryInterval { get; set; } = 1000;

    /// <summary>
    /// 是否启用自动重连
    /// </summary>
    public bool AutoReconnect { get; set; } = true;

    /// <summary>
    /// 心跳检测间隔（毫秒）
    /// </summary>
    public int HeartbeatInterval { get; set; } = 30000;
}
