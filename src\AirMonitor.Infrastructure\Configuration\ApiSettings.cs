namespace AirMonitor.Infrastructure.Configuration;

/// <summary>
/// API 配置设置
/// </summary>
public class ApiSettings
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Api";

    /// <summary>
    /// API 版本
    /// </summary>
    public string Version { get; set; } = "v1";

    /// <summary>
    /// API 标题
    /// </summary>
    public string Title { get; set; } = "AirMonitor API";

    /// <summary>
    /// API 描述
    /// </summary>
    public string Description { get; set; } = "商用空调监控系统 API";

    /// <summary>
    /// API 联系人信息
    /// </summary>
    public ContactInfo Contact { get; set; } = new();

    /// <summary>
    /// API 许可证信息
    /// </summary>
    public LicenseInfo License { get; set; } = new();

    /// <summary>
    /// 是否启用 Swagger UI
    /// </summary>
    public bool EnableSwaggerUI { get; set; } = true;

    /// <summary>
    /// 是否启用 API 版本控制
    /// </summary>
    public bool EnableVersioning { get; set; } = true;

    /// <summary>
    /// 默认 API 版本
    /// </summary>
    public string DefaultVersion { get; set; } = "1.0";

    /// <summary>
    /// API 请求限制配置
    /// </summary>
    public RateLimitSettings RateLimit { get; set; } = new();

    /// <summary>
    /// CORS 配置
    /// </summary>
    public CorsSettings Cors { get; set; } = new();
}

/// <summary>
/// 联系人信息
/// </summary>
public class ContactInfo
{
    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string Name { get; set; } = "AirMonitor Team";

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string Email { get; set; } = "<EMAIL>";

    /// <summary>
    /// 联系人网址
    /// </summary>
    public string Url { get; set; } = "https://www.airmonitor.com";
}

/// <summary>
/// 许可证信息
/// </summary>
public class LicenseInfo
{
    /// <summary>
    /// 许可证名称
    /// </summary>
    public string Name { get; set; } = "MIT";

    /// <summary>
    /// 许可证网址
    /// </summary>
    public string Url { get; set; } = "https://opensource.org/licenses/MIT";
}

/// <summary>
/// 请求限制配置
/// </summary>
public class RateLimitSettings
{
    /// <summary>
    /// 是否启用请求限制
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 每分钟请求限制
    /// </summary>
    public int RequestsPerMinute { get; set; } = 100;

    /// <summary>
    /// 每小时请求限制
    /// </summary>
    public int RequestsPerHour { get; set; } = 1000;

    /// <summary>
    /// 每天请求限制
    /// </summary>
    public int RequestsPerDay { get; set; } = 10000;
}

/// <summary>
/// CORS 配置
/// </summary>
public class CorsSettings
{
    /// <summary>
    /// 是否启用 CORS
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 允许的来源
    /// </summary>
    public string[] AllowedOrigins { get; set; } = { "*" };

    /// <summary>
    /// 允许的方法
    /// </summary>
    public string[] AllowedMethods { get; set; } = { "GET", "POST", "PUT", "DELETE", "OPTIONS" };

    /// <summary>
    /// 允许的请求头
    /// </summary>
    public string[] AllowedHeaders { get; set; } = { "*" };

    /// <summary>
    /// 是否允许凭据
    /// </summary>
    public bool AllowCredentials { get; set; } = true;

    /// <summary>
    /// 预检请求缓存时间（秒）
    /// </summary>
    public int PreflightMaxAge { get; set; } = 86400;
}
