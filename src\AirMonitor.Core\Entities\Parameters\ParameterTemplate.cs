using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Entities.Parameters;

/// <summary>
/// 参数模板实体
/// </summary>
public class ParameterTemplate
{
    /// <summary>
    /// 模板ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 模板描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 适用的设备类型
    /// </summary>
    public DeviceType ApplicableDeviceType { get; set; }
    
    /// <summary>
    /// 设备型号匹配模式
    /// </summary>
    public string ModelPattern { get; set; } = string.Empty;
    
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 参数定义集合
    /// </summary>
    public virtual ICollection<ParameterDefinition> ParameterDefinitions { get; set; } = new List<ParameterDefinition>();
}
