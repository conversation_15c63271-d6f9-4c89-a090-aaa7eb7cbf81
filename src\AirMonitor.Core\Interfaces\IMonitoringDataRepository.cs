using AirMonitor.Core.Entities.Monitoring;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 监控数据仓储接口
/// </summary>
public interface IMonitoringDataRepository
{
    /// <summary>
    /// 保存监控数据
    /// </summary>
    /// <param name="data">监控数据</param>
    /// <returns>保存的监控数据</returns>
    Task<MonitoringData> SaveAsync(MonitoringData data);
    
    /// <summary>
    /// 获取设备最新监控数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="count">数据条数</param>
    /// <returns>监控数据集合</returns>
    Task<IEnumerable<MonitoringData>> GetLatestByDeviceAsync(int deviceId, int count = 100);
    
    /// <summary>
    /// 根据时间范围获取监控数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>监控数据集合</returns>
    Task<IEnumerable<MonitoringData>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end);
    
    /// <summary>
    /// 获取参数最新数值
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>最新数值</returns>
    Task<decimal?> GetLatestNumericValueAsync(int deviceId, byte parameterIndex);
    
    /// <summary>
    /// 获取参数最新监控数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>最新监控数据</returns>
    Task<MonitoringData?> GetLatestByParameterAsync(int deviceId, byte parameterIndex);
}
