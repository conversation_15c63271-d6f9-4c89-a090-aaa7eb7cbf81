using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Entities.Parameters;

/// <summary>
/// 参数权限实体
/// </summary>
public class ParameterPermission
{
    /// <summary>
    /// 权限ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户角色
    /// </summary>
    public UserRole UserRole { get; set; }
    
    /// <summary>
    /// 参数定义ID
    /// </summary>
    public int ParameterDefinitionId { get; set; }
    
    /// <summary>
    /// 权限级别
    /// </summary>
    public PermissionLevel PermissionLevel { get; set; }
    
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 参数定义
    /// </summary>
    public virtual ParameterDefinition ParameterDefinition { get; set; } = null!;
}
