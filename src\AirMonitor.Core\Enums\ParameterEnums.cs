namespace AirMonitor.Core.Enums;

/// <summary>
/// 参数值格式枚举
/// </summary>
public enum ParameterValueFormat
{
    /// <summary>
    /// 单一参数值
    /// </summary>
    SingleValue = 1,
    
    /// <summary>
    /// 位域组合
    /// </summary>
    BitField = 2,
    
    /// <summary>
    /// 字节组合
    /// </summary>
    ByteCombination = 3,
    
    /// <summary>
    /// 混合格式
    /// </summary>
    MixedFormat = 4,
    
    /// <summary>
    /// 枚举值
    /// </summary>
    EnumValue = 5
}

/// <summary>
/// 系统模式优先级枚举
/// </summary>
public enum SystemModePriority
{
    /// <summary>
    /// 通常
    /// </summary>
    Normal = 0,
    
    /// <summary>
    /// 单冷
    /// </summary>
    CoolingOnly = 1,
    
    /// <summary>
    /// 单热
    /// </summary>
    HeatingOnly = 2,
    
    /// <summary>
    /// 冷优先
    /// </summary>
    CoolingPriority = 3,
    
    /// <summary>
    /// 热优先
    /// </summary>
    HeatingPriority = 4,
    
    /// <summary>
    /// 少数服从多数
    /// </summary>
    MajorityRule = 5
}

/// <summary>
/// 室外机工作模式枚举
/// </summary>
public enum OutdoorWorkMode
{
    /// <summary>
    /// 停止
    /// </summary>
    Stop = 0,
    
    /// <summary>
    /// 制冷
    /// </summary>
    Cooling = 1,
    
    /// <summary>
    /// 制热
    /// </summary>
    Heating = 2,
    
    /// <summary>
    /// 制热水
    /// </summary>
    HotWater = 3
}
