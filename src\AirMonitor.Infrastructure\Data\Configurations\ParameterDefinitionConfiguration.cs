using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Parameters;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 参数定义实体配置
/// </summary>
public class ParameterDefinitionConfiguration : IEntityTypeConfiguration<ParameterDefinition>
{
    public void Configure(EntityTypeBuilder<ParameterDefinition> builder)
    {
        builder.ToTable("ParameterDefinitions");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.CommandCode)
            .IsRequired()
            .HasComment("关联的命令码");
            
        builder.Property(x => x.ParameterIndex)
            .IsRequired()
            .HasComment("参数索引");
            
        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("参数名称");
            
        builder.Property(x => x.Description)
            .HasMaxLength(500)
            .HasComment("参数描述");
            
        builder.Property(x => x.Category)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("参数类别");
            
        builder.Property(x => x.DataType)
            .IsRequired()
            .HasMaxLength(20)
            .HasComment("数据类型");
            
        builder.Property(x => x.ValueFormat)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("参数值格式");
            
        builder.Property(x => x.FormatConfiguration)
            .HasColumnType("nvarchar(max)")
            .HasComment("格式配置JSON");
            
        builder.Property(x => x.Unit)
            .HasMaxLength(20)
            .HasComment("参数单位");
            
        builder.Property(x => x.MinValue)
            .HasPrecision(18, 6)
            .HasComment("最小值");
            
        builder.Property(x => x.MaxValue)
            .HasPrecision(18, 6)
            .HasComment("最大值");
        
        // 索引
        builder.HasIndex(x => new { x.CommandCode, x.ParameterIndex })
            .IsUnique()
            .HasDatabaseName("IX_ParameterDefinitions_Command_Index");
            
        builder.HasIndex(x => x.Category)
            .HasDatabaseName("IX_ParameterDefinitions_Category");
            
        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("IX_ParameterDefinitions_IsActive");
        
        // 关系配置
        builder.HasOne(x => x.ParameterTemplate)
            .WithMany(x => x.ParameterDefinitions)
            .HasForeignKey(x => x.ParameterTemplateId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.DeviceParameters)
            .WithOne(x => x.ParameterDefinition)
            .HasForeignKey(x => x.ParameterDefinitionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
