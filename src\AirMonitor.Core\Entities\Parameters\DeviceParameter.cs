using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Entities.Monitoring;

namespace AirMonitor.Core.Entities.Parameters;

/// <summary>
/// 设备参数实体
/// </summary>
public class DeviceParameter
{
    /// <summary>
    /// 设备参数ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public int DeviceId { get; set; }
    
    /// <summary>
    /// 参数定义ID
    /// </summary>
    public int ParameterDefinitionId { get; set; }
    
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }
    
    /// <summary>
    /// 自定义名称
    /// </summary>
    public string? CustomName { get; set; }
    
    /// <summary>
    /// 自定义配置
    /// </summary>
    public string? CustomConfiguration { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 设备
    /// </summary>
    public virtual Device Device { get; set; } = null!;
    
    /// <summary>
    /// 参数定义
    /// </summary>
    public virtual ParameterDefinition ParameterDefinition { get; set; } = null!;
    
    /// <summary>
    /// 监控数据集合
    /// </summary>
    public virtual ICollection<MonitoringData> MonitoringData { get; set; } = new List<MonitoringData>();
}
