using AirMonitor.Core.Enums;
using AirMonitor.Core.Entities.Devices;

namespace AirMonitor.Core.Entities.Users;

/// <summary>
/// 用户设备权限实体
/// </summary>
public class UserDevicePermission
{
    /// <summary>
    /// 权限ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public int DeviceId { get; set; }
    
    /// <summary>
    /// 权限级别
    /// </summary>
    public PermissionLevel PermissionLevel { get; set; }
    
    /// <summary>
    /// 授权时间
    /// </summary>
    public DateTimeOffset GrantedAt { get; set; }
    
    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTimeOffset? ExpiresAt { get; set; }
    
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 用户
    /// </summary>
    public virtual User User { get; set; } = null!;
    
    /// <summary>
    /// 设备
    /// </summary>
    public virtual Device Device { get; set; } = null!;
}
