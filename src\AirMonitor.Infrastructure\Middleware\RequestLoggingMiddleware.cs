using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Text;

namespace AirMonitor.Infrastructure.Middleware;

/// <summary>
/// 请求日志记录中间件
/// </summary>
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    /// <summary>
    /// 处理请求
    /// </summary>
    /// <param name="context">HTTP 上下文</param>
    /// <returns>任务</returns>
    public async Task InvokeAsync(HttpContext context)
    {
        // 跳过健康检查和静态文件的日志记录
        if (ShouldSkipLogging(context.Request.Path))
        {
            await _next(context);
            return;
        }

        var stopwatch = Stopwatch.StartNew();
        var correlationId = GetOrCreateCorrelationId(context);

        // 记录请求开始
        _logger.LogInformation(
            "开始处理请求 {CorrelationId}: {Method} {Path} {QueryString}",
            correlationId,
            context.Request.Method,
            context.Request.Path,
            context.Request.QueryString);

        // 记录请求体（仅对特定内容类型）
        await LogRequestBodyAsync(context, correlationId);

        // 保存原始响应体流
        var originalResponseBodyStream = context.Response.Body;

        try
        {
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;

            // 执行下一个中间件
            await _next(context);

            stopwatch.Stop();

            // 记录响应
            await LogResponseAsync(context, correlationId, stopwatch.ElapsedMilliseconds);

            // 复制响应体到原始流
            responseBodyStream.Seek(0, SeekOrigin.Begin);
            await responseBodyStream.CopyToAsync(originalResponseBodyStream);
        }
        finally
        {
            context.Response.Body = originalResponseBodyStream;
        }
    }

    /// <summary>
    /// 判断是否应该跳过日志记录
    /// </summary>
    /// <param name="path">请求路径</param>
    /// <returns>是否跳过</returns>
    private static bool ShouldSkipLogging(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant();
        return pathValue != null && (
            pathValue.StartsWith("/health") ||
            pathValue.StartsWith("/metrics") ||
            pathValue.StartsWith("/favicon.ico") ||
            pathValue.StartsWith("/swagger") ||
            pathValue.Contains("/css/") ||
            pathValue.Contains("/js/") ||
            pathValue.Contains("/images/"));
    }

    /// <summary>
    /// 获取或创建关联ID
    /// </summary>
    /// <param name="context">HTTP 上下文</param>
    /// <returns>关联ID</returns>
    private static string GetOrCreateCorrelationId(HttpContext context)
    {
        const string correlationIdHeader = "X-Correlation-ID";

        if (context.Request.Headers.TryGetValue(correlationIdHeader, out var correlationId))
        {
            return correlationId.ToString();
        }

        var newCorrelationId = Guid.NewGuid().ToString();
        context.Request.Headers[correlationIdHeader] = newCorrelationId;
        context.Response.Headers[correlationIdHeader] = newCorrelationId;

        return newCorrelationId;
    }

    /// <summary>
    /// 记录请求体
    /// </summary>
    /// <param name="context">HTTP 上下文</param>
    /// <param name="correlationId">关联ID</param>
    /// <returns>任务</returns>
    private async Task LogRequestBodyAsync(HttpContext context, string correlationId)
    {
        if (!ShouldLogRequestBody(context.Request))
        {
            return;
        }

        // 启用请求体缓冲（需要 Microsoft.AspNetCore.Http.Extensions）
        // context.Request.EnableBuffering();
        // 暂时跳过请求体读取，避免编译错误

        // 暂时跳过请求体读取，避免流位置问题
        _logger.LogDebug(
            "请求体记录已跳过 {CorrelationId}: ContentLength={ContentLength}",
            correlationId,
            context.Request.ContentLength ?? 0);
    }

    /// <summary>
    /// 记录响应
    /// </summary>
    /// <param name="context">HTTP 上下文</param>
    /// <param name="correlationId">关联ID</param>
    /// <param name="elapsedMilliseconds">耗时（毫秒）</param>
    /// <returns>任务</returns>
    private async Task LogResponseAsync(HttpContext context, string correlationId, long elapsedMilliseconds)
    {
        var statusCode = context.Response.StatusCode;
        var logLevel = GetLogLevel(statusCode);

        _logger.Log(
            logLevel,
            "完成处理请求 {CorrelationId}: {Method} {Path} - {StatusCode} - {ElapsedMs}ms",
            correlationId,
            context.Request.Method,
            context.Request.Path,
            statusCode,
            elapsedMilliseconds);

        // 记录响应体（仅对错误响应）
        if (statusCode >= 400 && ShouldLogResponseBody(context.Response))
        {
            context.Response.Body.Seek(0, SeekOrigin.Begin);
            var responseBody = await new StreamReader(context.Response.Body).ReadToEndAsync();
            context.Response.Body.Seek(0, SeekOrigin.Begin);

            if (!string.IsNullOrWhiteSpace(responseBody))
            {
                _logger.LogWarning(
                    "错误响应体 {CorrelationId}: {ResponseBody}",
                    correlationId,
                    responseBody);
            }
        }
    }

    /// <summary>
    /// 判断是否应该记录请求体
    /// </summary>
    /// <param name="request">HTTP 请求</param>
    /// <returns>是否记录</returns>
    private static bool ShouldLogRequestBody(HttpRequest request)
    {
        var contentType = request.ContentType?.ToLowerInvariant();
        return contentType != null && (
            contentType.Contains("application/json") ||
            contentType.Contains("application/xml") ||
            contentType.Contains("text/"));
    }

    /// <summary>
    /// 判断是否应该记录响应体
    /// </summary>
    /// <param name="response">HTTP 响应</param>
    /// <returns>是否记录</returns>
    private static bool ShouldLogResponseBody(HttpResponse response)
    {
        var contentType = response.ContentType?.ToLowerInvariant();
        return contentType != null && (
            contentType.Contains("application/json") ||
            contentType.Contains("application/xml") ||
            contentType.Contains("text/"));
    }

    /// <summary>
    /// 根据状态码获取日志级别
    /// </summary>
    /// <param name="statusCode">状态码</param>
    /// <returns>日志级别</returns>
    private static LogLevel GetLogLevel(int statusCode)
    {
        return statusCode switch
        {
            >= 500 => LogLevel.Error,
            >= 400 => LogLevel.Warning,
            _ => LogLevel.Information
        };
    }
}
