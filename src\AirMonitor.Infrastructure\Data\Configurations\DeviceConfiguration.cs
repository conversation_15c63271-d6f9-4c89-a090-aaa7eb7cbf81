using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Devices;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 设备实体配置
/// </summary>
public class DeviceConfiguration : IEntityTypeConfiguration<Device>
{
    public void Configure(EntityTypeBuilder<Device> builder)
    {
        builder.ToTable("Devices");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.DeviceAddress)
            .IsRequired()
            .HasComment("RS485设备地址");
            
        builder.Property(x => x.DeviceType)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("设备类型");
            
        builder.Property(x => x.SerialNumber)
            .HasMaxLength(100)
            .HasComment("设备序列号");
            
        builder.Property(x => x.ModelNumber)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("设备型号");
            
        builder.Property(x => x.FirmwareVersion)
            .IsRequired()
            .HasMaxLength(20)
            .HasComment("固件版本");
            
        builder.Property(x => x.Status)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("设备状态");
            
        builder.Property(x => x.Location)
            .HasMaxLength(200)
            .HasComment("设备位置");
            
        builder.Property(x => x.Description)
            .HasMaxLength(500)
            .HasComment("设备描述");
        
        // 索引
        builder.HasIndex(x => x.DeviceAddress)
            .IsUnique()
            .HasDatabaseName("IX_Devices_DeviceAddress");
            
        builder.HasIndex(x => x.SerialNumber)
            .HasDatabaseName("IX_Devices_SerialNumber");
            
        builder.HasIndex(x => x.DeviceType)
            .HasDatabaseName("IX_Devices_DeviceType");
            
        builder.HasIndex(x => x.IsOnline)
            .HasDatabaseName("IX_Devices_IsOnline");
        
        // 关系配置
        builder.HasMany(x => x.DeviceParameters)
            .WithOne(x => x.Device)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.CommunicationLogs)
            .WithOne(x => x.Device)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.MonitoringData)
            .WithOne(x => x.Device)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasMany(x => x.UserPermissions)
            .WithOne(x => x.Device)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
