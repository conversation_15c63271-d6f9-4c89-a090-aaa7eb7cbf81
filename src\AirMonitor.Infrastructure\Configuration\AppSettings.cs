namespace AirMonitor.Infrastructure.Configuration;

/// <summary>
/// 应用程序配置设置
/// </summary>
public class AppSettings
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "App";

    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string Name { get; set; } = "AirMonitor";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 应用程序环境
    /// </summary>
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// 是否启用调试模式
    /// </summary>
    public bool DebugMode { get; set; } = false;

    /// <summary>
    /// 数据采集间隔（毫秒）
    /// </summary>
    public int DataCollectionInterval { get; set; } = 5000;

    /// <summary>
    /// 数据批量处理大小
    /// </summary>
    public int BatchProcessingSize { get; set; } = 100;

    /// <summary>
    /// 最大并发设备数
    /// </summary>
    public int MaxConcurrentDevices { get; set; } = 50;

    /// <summary>
    /// 数据保留天数
    /// </summary>
    public int DataRetentionDays { get; set; } = 365;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 是否启用健康检查
    /// </summary>
    public bool EnableHealthChecks { get; set; } = true;

    /// <summary>
    /// 健康检查间隔（秒）
    /// </summary>
    public int HealthCheckInterval { get; set; } = 30;

    /// <summary>
    /// 临时文件目录
    /// </summary>
    public string TempDirectory { get; set; } = "temp";

    /// <summary>
    /// 日志文件目录
    /// </summary>
    public string LogDirectory { get; set; } = "logs";

    /// <summary>
    /// 备份文件目录
    /// </summary>
    public string BackupDirectory { get; set; } = "backup";
}
