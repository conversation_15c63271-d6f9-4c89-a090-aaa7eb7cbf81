using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Interfaces;

namespace AirMonitor.Infrastructure.Data.Repositories;

/// <summary>
/// 设备参数仓储实现
/// </summary>
public class DeviceParameterRepository : IDeviceParameterRepository
{
    private readonly AirMonitorDbContext _context;

    public DeviceParameterRepository(AirMonitorDbContext context)
    {
        _context = context;
    }

    public async Task<DeviceParameter> GetOrCreateAsync(int deviceId, int parameterDefinitionId)
    {
        var existing = await GetByDeviceAndParameterAsync(deviceId, parameterDefinitionId);
        if (existing != null)
        {
            return existing;
        }

        var deviceParameter = new DeviceParameter
        {
            DeviceId = deviceId,
            ParameterDefinitionId = parameterDefinitionId,
            IsEnabled = true,
            CreatedAt = DateTimeOffset.UtcNow,
            UpdatedAt = DateTimeOffset.UtcNow
        };

        return await CreateAsync(deviceParameter);
    }

    public async Task<DeviceParameter?> GetByDeviceAndParameterAsync(int deviceId, int parameterDefinitionId)
    {
        return await _context.DeviceParameters
            .Include(dp => dp.Device)
            .Include(dp => dp.ParameterDefinition)
            .FirstOrDefaultAsync(dp => dp.DeviceId == deviceId && dp.ParameterDefinitionId == parameterDefinitionId);
    }

    public async Task<DeviceParameter> CreateAsync(DeviceParameter deviceParameter)
    {
        deviceParameter.CreatedAt = DateTimeOffset.UtcNow;
        deviceParameter.UpdatedAt = DateTimeOffset.UtcNow;
        
        _context.DeviceParameters.Add(deviceParameter);
        await _context.SaveChangesAsync();
        return deviceParameter;
    }

    public async Task<IEnumerable<DeviceParameter>> GetByDeviceAsync(int deviceId)
    {
        return await _context.DeviceParameters
            .Include(dp => dp.ParameterDefinition)
            .Where(dp => dp.DeviceId == deviceId && dp.IsEnabled)
            .ToListAsync();
    }
}
