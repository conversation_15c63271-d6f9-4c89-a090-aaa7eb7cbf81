# 数据模型设计开发文档

## 1. 功能需求描述

### 1.1 业务背景
IoT监控系统需要管理通过RS485通信协议连接的空调设备网络，包括室外机、室内机和集中控制器。系统采用主从轮询架构，主设备（通常为外机F1）通过RS485总线轮询各从设备，收集运行参数和状态信息。

### 1.2 功能范围
- **设备管理**: 动态发现和管理RS485总线上的各类设备
- **通信协议支持**: 支持两种RS485数据帧格式的解析和存储
- **参数管理**: 支持参数索引表的动态配置和多格式参数值解析
- **数据存储**: 灵活存储不同设备类型的监控数据
- **权限控制**: 基于角色的设备访问权限管理
- **状态监控**: 设备在线/离线状态跟踪

### 1.3 用户故事
- **作为系统管理员**，我需要管理系统用户、角色权限、参数模板配置等系统管理功能，同时也能连接和监控所有RS485设备
- **作为研发人员**，我需要监控RS485总线上的所有设备及其完整参数，进行调试和分析
- **作为服务人员**，我需要监控RS485总线上所有设备的部分授权参数，进行设备维护
- **作为普通用户**，我需要监控RS485总线上设备的基础参数（比服务人员权限更少），进行基本的查看和控制

## 2. 技术实现方案

### 2.1 架构设计
数据模型采用分层设计，包含以下核心层次：
- **设备层**: 管理物理设备和通信连接
- **协议层**: 处理RS485通信协议和数据帧解析
- **参数层**: 管理参数定义和值存储
- **权限层**: 控制用户对设备和参数的访问权限

### 2.2 技术选型
- **ORM框架**: Entity Framework Core 8.0
- **数据库**: SQL Server / PostgreSQL
- **数据注解**: System.ComponentModel.DataAnnotations
- **JSON序列化**: System.Text.Json
- **时间处理**: DateTimeOffset for UTC timestamps

### 2.3 设计模式
- **仓储模式**: 数据访问抽象
- **工厂模式**: 协议解析器创建
- **策略模式**: 参数值格式解析
- **观察者模式**: 设备状态变更通知

## 3. 数据模型核心实体设计

### 3.1 设备管理实体

#### 3.1.1 Device (RS485设备)
```csharp
public class Device
{
    public int Id { get; set; }
    public byte DeviceAddress { get; set; }
    public DeviceType DeviceType { get; set; }
    public string? SerialNumber { get; set; }
    public string ModelNumber { get; set; } = string.Empty;
    public string FirmwareVersion { get; set; } = string.Empty;
    public DeviceStatus Status { get; set; }
    public DateTimeOffset LastCommunication { get; set; }
    public bool IsOnline { get; set; }
    public string? Location { get; set; }
    public string? Description { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }

    // 导航属性
    public virtual ICollection<DeviceParameter> DeviceParameters { get; set; } = new List<DeviceParameter>();
    public virtual ICollection<CommunicationLog> CommunicationLogs { get; set; } = new List<CommunicationLog>();
    public virtual ICollection<MonitoringData> MonitoringData { get; set; } = new List<MonitoringData>();
    public virtual ICollection<UserDevicePermission> UserPermissions { get; set; } = new List<UserDevicePermission>();
}
```

### 3.2 通信协议实体

#### 3.2.1 CommunicationLog (通信日志)
```csharp
public class CommunicationLog
{
    public long Id { get; set; }
    public int DeviceId { get; set; }
    public CommunicationType CommunicationType { get; set; }
    public ProtocolFormat ProtocolFormat { get; set; }
    public byte SourceAddress { get; set; }
    public byte TargetAddress { get; set; }
    public byte CommandCode { get; set; }
    public byte[] RawData { get; set; } = Array.Empty<byte>();
    public string? ParsedData { get; set; }
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    
    // 导航属性
    public virtual Device Device { get; set; } = null!;
}
```

### 3.3 参数管理实体

#### 3.3.1 ParameterTemplate (参数模板)
```csharp
public class ParameterTemplate
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DeviceType ApplicableDeviceType { get; set; }
    public string ModelPattern { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    public virtual ICollection<ParameterDefinition> ParameterDefinitions { get; set; } = new List<ParameterDefinition>();
}
```

#### 3.3.2 ParameterDefinition (参数定义)
```csharp
public class ParameterDefinition
{
    public int Id { get; set; }
    public int ParameterTemplateId { get; set; }
    public byte CommandCode { get; set; }        // 新增：关联的命令码
    public byte ParameterIndex { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;  // 新增：数据类型(u16,s16,u32,s32)
    public ParameterValueFormat ValueFormat { get; set; }
    public string? FormatConfiguration { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool IsReadOnly { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }

    // 导航属性
    public virtual ParameterTemplate ParameterTemplate { get; set; } = null!;
    public virtual ICollection<DeviceParameter> DeviceParameters { get; set; } = new List<DeviceParameter>();
}
```

#### 3.3.3 DeviceParameter (设备参数)
```csharp
public class DeviceParameter
{
    public int Id { get; set; }
    public int DeviceId { get; set; }
    public int ParameterDefinitionId { get; set; }
    public bool IsEnabled { get; set; }
    public string? CustomName { get; set; }
    public string? CustomConfiguration { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }

    // 导航属性
    public virtual Device Device { get; set; } = null!;
    public virtual ParameterDefinition ParameterDefinition { get; set; } = null!;
    public virtual ICollection<MonitoringData> MonitoringData { get; set; } = new List<MonitoringData>();
}
```

### 3.4 监控数据实体

#### 3.4.1 MonitoringData (监控数据)
```csharp
public class MonitoringData
{
    public long Id { get; set; }
    public int DeviceId { get; set; }
    public int DeviceParameterId { get; set; }
    public byte ParameterIndex { get; set; }
    public ushort RawValue { get; set; }
    public string? ParsedValue { get; set; }
    public decimal? NumericValue { get; set; }
    public string? StringValue { get; set; }
    public bool? BooleanValue { get; set; }
    public DataQuality Quality { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    public DateTimeOffset CreatedAt { get; set; }

    // 导航属性
    public virtual Device Device { get; set; } = null!;
    public virtual DeviceParameter DeviceParameter { get; set; } = null!;
}
```

### 3.5 权限管理实体

#### 3.5.1 User (用户)
```csharp
public class User
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    public DateTimeOffset? LastLoginAt { get; set; }

    // 导航属性
    public virtual ICollection<UserDevicePermission> DevicePermissions { get; set; } = new List<UserDevicePermission>();
}
```

#### 3.5.2 UserDevicePermission (用户设备权限)
```csharp
public class UserDevicePermission
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int DeviceId { get; set; }
    public PermissionLevel PermissionLevel { get; set; }
    public DateTimeOffset GrantedAt { get; set; }
    public DateTimeOffset? ExpiresAt { get; set; }
    public bool IsActive { get; set; }

    // 导航属性
    public virtual User User { get; set; } = null!;
    public virtual Device Device { get; set; } = null!;
}
```

## 4. 枚举类型定义

### 4.1 设备相关枚举
```csharp
public enum DeviceType
{
    OutdoorUnit = 1,    // 室外机
    IndoorUnit = 2,     // 室内机
    Controller = 3      // 集中控制器
}

public enum DeviceStatus
{
    Unknown = 0,        // 未知状态
    Normal = 1,         // 正常运行
    Warning = 2,        // 警告状态
    Error = 3,          // 错误状态
    Maintenance = 4     // 维护状态
}
```

### 4.2 通信相关枚举
```csharp
public enum CommunicationType
{
    PointRead = 1,      // 点播读取
    ControlCommand = 2  // 控制命令
}

public enum ProtocolFormat
{
    ParameterIndex = 1, // 参数索引格式（变长）
    CustomData = 2      // 自定义数据格式（定长）
}
```

### 4.3 参数相关枚举
```csharp
public enum ParameterValueFormat
{
    SingleValue = 1,    // 单一参数值
    BitField = 2,       // 位域组合
    ByteCombination = 3, // 字节组合
    MixedFormat = 4,    // 混合格式
    EnumValue = 5       // 枚举值
}

public enum DataQuality
{
    Good = 1,           // 数据良好
    Uncertain = 2,      // 数据不确定
    Bad = 3             // 数据错误
}
```

### 4.4 权限相关枚举
```csharp
public enum UserRole
{
    SystemAdmin = 1,    // 系统管理员
    RDPersonnel = 2,    // 研发人员
    ServicePersonnel = 3, // 服务人员
    RegularUser = 4     // 普通用户
}

public enum PermissionLevel
{
    None = 0,           // 无权限
    ReadOnly = 1,       // 只读
    ReadWrite = 2,      // 读写
    FullControl = 3     // 完全控制
}
```

## 5. DbContext 配置

### 5.1 AirMonitorDbContext
```csharp
public class AirMonitorDbContext : DbContext
{
    public AirMonitorDbContext(DbContextOptions<AirMonitorDbContext> options) : base(options) { }

    // DbSet 定义
    public DbSet<Device> Devices { get; set; }
    public DbSet<ParameterTemplate> ParameterTemplates { get; set; }
    public DbSet<ParameterDefinition> ParameterDefinitions { get; set; }
    public DbSet<DeviceParameter> DeviceParameters { get; set; }
    public DbSet<MonitoringData> MonitoringData { get; set; }
    public DbSet<CommunicationLog> CommunicationLogs { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserDevicePermission> UserDevicePermissions { get; set; }
    public DbSet<ParameterPermission> ParameterPermissions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 应用所有配置
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(AirMonitorDbContext).Assembly);

        // 全局查询过滤器
        modelBuilder.Entity<User>().HasQueryFilter(x => x.IsActive);
    }
}
```

### 5.2 实体配置示例

#### 5.2.1 DeviceConfiguration
```csharp
public class DeviceConfiguration : IEntityTypeConfiguration<Device>
{
    public void Configure(EntityTypeBuilder<Device> builder)
    {
        builder.ToTable("Devices");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.DeviceAddress)
            .IsRequired()
            .HasComment("RS485设备地址");

        builder.Property(x => x.ModelNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.FirmwareVersion)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.SerialNumber)
            .HasMaxLength(100);

        // 索引
        builder.HasIndex(x => x.DeviceAddress)
            .IsUnique()
            .HasDatabaseName("IX_Devices_DeviceAddress");

        builder.HasIndex(x => x.SerialNumber)
            .HasDatabaseName("IX_Devices_SerialNumber");

        // 关系配置
        builder.HasMany(x => x.UserPermissions)
            .WithOne(x => x.Device)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
```

## 6. 数据访问层设计

### 6.1 仓储接口定义
```csharp
public interface IDeviceRepository
{
    Task<Device?> GetByAddressAsync(byte deviceAddress);
    Task<IEnumerable<Device>> GetOnlineDevicesAsync();
    Task<IEnumerable<Device>> GetByTypeAsync(DeviceType deviceType);
    Task UpdateOnlineStatusAsync(byte deviceAddress, bool isOnline);
    Task<Device> CreateAsync(Device device);
    Task<Device> UpdateAsync(Device device);
}

public interface IMonitoringDataRepository
{
    Task<MonitoringData> SaveAsync(MonitoringData data);
    Task<IEnumerable<MonitoringData>> GetLatestByDeviceAsync(int deviceId, int count = 100);
    Task<IEnumerable<MonitoringData>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end);
    Task<decimal?> GetLatestNumericValueAsync(int deviceId, byte parameterIndex);
}

public interface IParameterTemplateRepository
{
    Task<ParameterTemplate?> GetByDeviceTypeAndModelAsync(DeviceType deviceType, string modelPattern);
    Task<IEnumerable<ParameterDefinition>> GetParameterDefinitionsAsync(int templateId);
    Task<ParameterDefinition?> GetParameterDefinitionAsync(int templateId, byte parameterIndex);
}

public interface IParameterDefinitionRepository
{
    Task<ParameterDefinition?> GetByCommandAndIndexAsync(byte commandCode, byte parameterIndex);
    Task<ParameterDefinition?> GetDefinitionByIdAsync(int id);
    Task<IEnumerable<ParameterDefinition>> GetByDeviceTypeAsync(DeviceType deviceType);
    Task<ParameterDefinition?> GetByDeviceAndIndexAsync(int deviceId, byte parameterIndex);
}

public interface IDeviceParameterRepository
{
    Task<DeviceParameter> GetOrCreateAsync(int deviceId, int parameterDefinitionId);
    Task<DeviceParameter?> GetByDeviceAndParameterAsync(int deviceId, int parameterDefinitionId);
    Task<DeviceParameter> CreateAsync(DeviceParameter deviceParameter);
}

public interface ICommunicationLogRepository
{
    Task<CommunicationLog> SaveAsync(CommunicationLog log);
    Task<IEnumerable<CommunicationLog>> GetByDeviceAsync(int deviceId, int count = 100);
    Task<IEnumerable<CommunicationLog>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end);
}
```

### 6.2 协议解析服务接口
```csharp
public interface IProtocolParserService
{
    ParseResult ParseFrame(byte[] rawData);
    byte[] BuildFrame(byte sourceAddress, byte targetAddress, byte commandCode, byte[] data);
    bool ValidateCRC(byte[] frame);
}

// ParseResult和ParameterData类定义已在7.1.4节中定义
```

## 7. RS485协议数据解析实现

### 7.1 协议格式识别与解析流程

#### 7.1.1 协议格式自动识别
```csharp
public class ProtocolFormatDetector
{
    public static ProtocolFormat DetectFormat(byte[] rawData, byte commandCode)
    {
        // 根据命令码和数据结构特征判断格式
        if (commandCode == 0x12 || commandCode == 0x13) // 参数读写命令
        {
            return ProtocolFormat.ParameterIndex;
        }
        else if (commandCode == 0xA1 || commandCode == 0xA2) // 状态数据命令
        {
            return ProtocolFormat.CustomData;
        }

        // 通过数据长度和结构特征进一步判断
        if (rawData.Length >= 8 && rawData[5] < 0x80 && rawData[6] < 0x20)
        {
            // 可能是参数索引格式：索引号 < 128，参数个数 < 32
            return ProtocolFormat.ParameterIndex;
        }

        return ProtocolFormat.CustomData;
    }
}
```

#### 7.1.2 格式1 - 参数索引格式解析（变长）
```csharp
public class Format1Parser : IProtocolParser
{
    public ParseResult ParseFrame(byte[] rawData)
    {
        // 实际示例：7E F1 F1 12 25 35 0E 01 4F 00 E3 00 F1 02 90...40 EC
        if (rawData.Length < 8)
            return new ParseResult { IsValid = false, ErrorMessage = "参数索引格式帧长度不足" };

        var result = new ParseResult
        {
            Format = ProtocolFormat.ParameterIndex,
            SourceAddress = rawData[1],      // F1
            TargetAddress = rawData[2],      // F1
            CommandCode = rawData[3],        // 12
            DataLength = rawData[4]          // 25 (总长度)
        };

        // 解析参数索引和个数
        byte parameterIndex = rawData[5];    // 35 (起始参数索引)
        byte parameterCount = rawData[6];    // 0E (14个参数)

        var parameters = new List<ParameterData>();
        int dataOffset = 7; // 参数数据起始位置

        for (int i = 0; i < parameterCount; i++)
        {
            if (dataOffset + 1 >= rawData.Length - 2) break; // 预留CRC空间

            // 高字节在前的16位参数值
            ushort rawValue = (ushort)((rawData[dataOffset] << 8) | rawData[dataOffset + 1]);
            parameters.Add(new ParameterData
            {
                Index = (byte)(parameterIndex + i),
                RawValue = rawValue
            });
            dataOffset += 2;
        }

        result.Parameters = parameters.ToArray();
        result.IsValid = ValidateModbusCRC(rawData);
        return result;
    }
}

// 实际解析示例：
// 原始数据: 7E F1 F1 12 25 35 0E 01 4F 00 E3 00 F1 02 90 00 00 00 00 00 00 00 00 00 00 00 00 00 3B 10 40 00 00 00 00 40 EC
// 解析结果:
// - 源地址: F1 (主外机)
// - 目标地址: F1 (自身)
// - 命令码: 12 (参数读取响应)
// - 数据长度: 25 (37字节总长度)
// - 参数索引: 35 (0x35)
// - 参数个数: 0E (14个参数)
// - 参数0x35值: 0x014F (额定容量，通过参数定义表解析为33.5kW)
// - 参数0x36值: 0x00E3 (板换气进温度Tgi，解析为22.7°C)
// - 参数0x37值: 0x00F1 (下一个参数值)
// - CRC16: 40 EC (Modbus CRC16，计算范围：F1到00)
```

#### 7.1.3 格式2 - 自定义数据格式解析（定长）
```csharp
public class Format2Parser : IProtocolParser
{
    public ParseResult ParseFrame(byte[] rawData)
    {
        // 实际示例：7E F1 00 A1 17 23 60 02 1C 00 1E FF F6 01 9C 00 01 17 18 01 00 CA C3
        if (rawData.Length < 7)
            return new ParseResult { IsValid = false, ErrorMessage = "自定义数据格式帧长度不足" };

        var result = new ParseResult
        {
            Format = ProtocolFormat.CustomData,
            SourceAddress = rawData[1],      // F1
            TargetAddress = rawData[2],      // 00
            CommandCode = rawData[3],        // A1
            DataLength = rawData[4]          // 17 (总长度)
        };

        // 提取自定义数据区（从第5字节到CRC前）
        int customDataLength = rawData[4] - 7; // 总长度减去固定部分(头码+源+目标+命令+长度+CRC2)
        byte[] customData = new byte[customDataLength];
        Array.Copy(rawData, 5, customData, 0, customDataLength);

        result.Data = customData;
        result.IsValid = ValidateModbusCRC(rawData);
        return result;
    }

    private bool ValidateModbusCRC(byte[] frame)
    {
        if (frame.Length < 3) return false;

        // CRC计算范围：从源地址到数据结束（不包括头码和CRC本身）
        int crcStart = 1; // 从源地址开始
        int crcLength = frame.Length - 3; // 减去头码和2字节CRC

        ushort calculatedCRC = CalculateModbusCRC16(frame, crcStart, crcLength);
        ushort frameCRC = (ushort)((frame[frame.Length - 2] << 8) | frame[frame.Length - 1]);

        return calculatedCRC == frameCRC;
    }

    private ushort CalculateModbusCRC16(byte[] data, int start, int length)
    {
        ushort crc = 0xFFFF;

        for (int i = start; i < start + length; i++)
        {
            crc ^= data[i];
            for (int j = 0; j < 8; j++)
            {
                if ((crc & 0x0001) != 0)
                {
                    crc >>= 1;
                    crc ^= 0xA001;
                }
                else
                {
                    crc >>= 1;
                }
            }
        }

        return crc;
    }
}

// 实际解析示例：
// 原始数据: 7E F1 00 A1 17 23 60 02 1C 00 1E FF F6 01 9C 00 01 17 18 01 00 CA C3
// 解析结果:
// - 源地址: F1 (主外机)
// - 目标地址: 00 (广播地址)
// - 命令码: A1 (系统状态数据)
// - 数据长度: 17 (23字节总长度)
// - 自定义数据: 23 60 02 1C 00 1E FF F6 01 9C 00 01 17 18 01 00 (16字节)
// - CRC16: CA C3 (Modbus CRC16，计算范围：F1到00)

#### 7.1.4 辅助类定义
```csharp
// 解析结果类
public class ParseResult
{
    public bool IsValid { get; set; }
    public ProtocolFormat Format { get; set; }
    public byte SourceAddress { get; set; }
    public byte TargetAddress { get; set; }
    public byte CommandCode { get; set; }
    public byte DataLength { get; set; }
    public byte[]? Data { get; set; }
    public ParameterData[]? Parameters { get; set; }
    public string? ErrorMessage { get; set; }
}

// 参数数据类
public class ParameterData
{
    public byte Index { get; set; }
    public ushort RawValue { get; set; }
    public object? ParsedValue { get; set; }
}

// 解析后的参数类
public class ParsedParameter
{
    public byte Index { get; set; }
    public ushort RawValue { get; set; }
    public ParameterDefinition Definition { get; set; } = null!;
    public object? ParsedValue { get; set; }
}

// 格式2自定义数据解析结果
public class CustomDataResult
{
    public SystemFlags SystemFlags { get; set; } = new();
    public OutdoorStatus OutdoorStatus { get; set; } = new();
    public decimal HighPressureSaturationTemp { get; set; }
    public decimal LowPressureSaturationTemp { get; set; }
}

// 系统标志位
public class SystemFlags
{
    public bool SupportsTempCorrection { get; set; }
    public bool AllowAutoAddressing { get; set; }
    public bool IndoorAddressingMode { get; set; }
    public bool ProhibitThermoOnConversion { get; set; }
    public bool EnergyLock26Degree { get; set; }
    public SystemModePriority SystemModePriority { get; set; }
}

// 室外机状态
public class OutdoorStatus
{
    public OutdoorWorkMode WorkMode { get; set; }
    public bool CompressorRunning { get; set; }
    public bool OilReturnStatus { get; set; }
    public bool DefrostStatus { get; set; }
    public bool DefrostPrepareStatus { get; set; }
    public bool FourWayValveStatus { get; set; }
    public bool HeatingOilReturnMode { get; set; }
}

// 枚举定义
public enum SystemModePriority
{
    Normal = 0,
    CoolingOnly = 1,
    HeatingOnly = 2,
    CoolingPriority = 3,
    HeatingPriority = 4,
    MajorityRule = 5
}

public enum OutdoorWorkMode
{
    Stop = 0,
    Cooling = 1,
    Heating = 2,
    HotWater = 3
}
### 7.2 详细数据解析实现

#### 7.2.1 格式2自定义数据详细解析
```csharp
public class Format2DataParser
{
    public CustomDataResult ParseA1Command(byte[] customData)
    {
        // 解析A1命令的16字节自定义数据
        // 数据: 23 60 02 1C 00 1E FF F6 01 9C 00 01 17 18 01 00

        var result = new CustomDataResult();

        // 第1字节（0x23）位域解析
        result.SystemFlags = ParseSystemFlags(customData[0]);

        // 第2字节（0x60）位域解析
        result.OutdoorStatus = ParseOutdoorStatus(customData[1]);

        // 第3-4字节（0x02 1C）组合解析：外机系统高压饱和温度
        result.HighPressureSaturationTemp = ParseTemperature(customData[2], customData[3]);

        // 第5-6字节（0x00 1E）组合解析：外机系统低压饱和温度
        result.LowPressureSaturationTemp = ParseTemperature(customData[4], customData[5]);

        // 继续解析其他字节...

        return result;
    }

    private SystemFlags ParseSystemFlags(byte flagByte)
    {
        // 解析0x23 = 0010 0011
        return new SystemFlags
        {
            SupportsTempCorrection = (flagByte & 0x80) != 0,      // bit7: 0
            AllowAutoAddressing = (flagByte & 0x40) != 0,         // bit6: 0
            IndoorAddressingMode = (flagByte & 0x20) != 0,        // bit5: 1
            ProhibitThermoOnConversion = (flagByte & 0x10) != 0,  // bit4: 0
            EnergyLock26Degree = (flagByte & 0x08) != 0,          // bit3: 0
            SystemModePriority = (SystemModePriority)(flagByte & 0x07) // bit2-0: 011 = 3
        };
    }

    private OutdoorStatus ParseOutdoorStatus(byte statusByte)
    {
        // 解析0x60 = 0110 0000
        return new OutdoorStatus
        {
            WorkMode = (OutdoorWorkMode)((statusByte >> 6) & 0x03),    // bit7-6: 01 = 制冷
            CompressorRunning = (statusByte & 0x20) != 0,              // bit5: 1
            OilReturnStatus = (statusByte & 0x10) != 0,                // bit4: 0
            DefrostStatus = (statusByte & 0x08) != 0,                  // bit3: 0
            DefrostPrepareStatus = (statusByte & 0x04) != 0,           // bit2: 0
            FourWayValveStatus = (statusByte & 0x02) != 0,             // bit1: 0
            HeatingOilReturnMode = (statusByte & 0x01) != 0            // bit0: 0
        };
    }

    private decimal ParseTemperature(byte highByte, byte lowByte)
    {
        // 高字节在前，精度0.1度
        ushort rawValue = (ushort)((highByte << 8) | lowByte);
        return rawValue / 10.0m;
    }
}
```

#### 7.2.2 格式1参数索引解析实现
```csharp
public class Format1ParameterParser
{
    private readonly IParameterDefinitionRepository _parameterRepository;

    public async Task<List<ParsedParameter>> ParseParametersAsync(
        byte commandCode, byte startIndex, ParameterData[] parameters)
    {
        var results = new List<ParsedParameter>();

        foreach (var param in parameters)
        {
            // 通过命令码和参数索引查询参数定义
            var definition = await _parameterRepository.GetByCommandAndIndexAsync(commandCode, param.Index);
            if (definition == null) continue;

            var parsedParam = new ParsedParameter
            {
                Index = param.Index,
                RawValue = param.RawValue,
                Definition = definition
            };

            // 根据参数定义的格式进行解析
            switch (definition.ValueFormat)
            {
                case ParameterValueFormat.SingleValue:
                    parsedParam.ParsedValue = ParseSingleValue(param.RawValue, definition);
                    break;
                case ParameterValueFormat.BitField:
                    parsedParam.ParsedValue = ParseBitField(param.RawValue, definition);
                    break;
                case ParameterValueFormat.ByteCombination:
                    parsedParam.ParsedValue = ParseByteCombination(param.RawValue, definition);
                    break;
                case ParameterValueFormat.MixedFormat:
                    parsedParam.ParsedValue = ParseMixedFormat(param.RawValue, definition);
                    break;
            }

            results.Add(parsedParam);
        }

        return results;
    }

    private object ParseSingleValue(ushort rawValue, ParameterDefinition definition)
    {
        // 示例：参数0x35 = 0x014F，定义为"额定容量"，格式u16，单位100W
        var config = JsonSerializer.Deserialize<SingleValueConfig>(definition.FormatConfiguration);

        if (definition.DataType == "u16")
        {
            return rawValue * config.Multiplier; // 0x014F * 100 = 33500W = 33.5kW
        }
        else if (definition.DataType == "s16")
        {
            // 有符号16位整数
            short signedValue = (short)rawValue;
            return signedValue * config.Multiplier;
        }

        return rawValue;
    }

    private object ParseBitField(ushort rawValue, ParameterDefinition definition)
    {
        // 示例：参数0x27的位域解析
        var config = JsonSerializer.Deserialize<BitFieldConfig>(definition.FormatConfiguration);
        var result = new Dictionary<string, object>();

        foreach (var bitDef in config.BitDefinitions)
        {
            if (bitDef.BitCount == 1)
            {
                // 单个位
                result[bitDef.Name] = (rawValue & (1 << bitDef.StartBit)) != 0;
            }
            else
            {
                // 多位字段
                int mask = (1 << bitDef.BitCount) - 1;
                int value = (rawValue >> bitDef.StartBit) & mask;
                result[bitDef.Name] = value;
            }
        }

        return result;
    }
}

// 参数定义配置示例
public class SingleValueConfig
{
    public string DataType { get; set; } = "u16"; // u16, s16, u32, s32
    public decimal Multiplier { get; set; } = 1;
    public decimal Offset { get; set; } = 0;
    public int DecimalPlaces { get; set; } = 0;
}

public class BitFieldConfig
{
    public BitDefinition[] BitDefinitions { get; set; } = Array.Empty<BitDefinition>();
}

public class BitDefinition
{
    public string Name { get; set; } = string.Empty;
    public int StartBit { get; set; }
    public int BitCount { get; set; } = 1;
    public string[] EnumValues { get; set; } = Array.Empty<string>();
}
```

### 7.3 参数定义表管理

#### 7.3.1 参数定义种子数据示例
```csharp
public static class ParameterDefinitionSeedData
{
    public static ParameterDefinition[] GetFormat1Parameters()
    {
        return new[]
        {
            new ParameterDefinition
            {
                CommandCode = 0x12,
                ParameterIndex = 0x35,
                Name = "额定容量",
                Description = "设备额定制冷/制热容量",
                Category = "Capacity",
                DataType = "u16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = JsonSerializer.Serialize(new SingleValueConfig
                {
                    DataType = "u16",
                    Multiplier = 100,
                    DecimalPlaces = 1
                }),
                Unit = "W",
                IsReadOnly = true
            },
            new ParameterDefinition
            {
                CommandCode = 0x12,
                ParameterIndex = 0x36,
                Name = "板换气进温度Tgi",
                Description = "板式换热器气体进口温度",
                Category = "Temperature",
                DataType = "s16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = JsonSerializer.Serialize(new SingleValueConfig
                {
                    DataType = "s16",
                    Multiplier = 0.1m,
                    DecimalPlaces = 1
                }),
                Unit = "°C",
                IsReadOnly = true
            },
            new ParameterDefinition
            {
                CommandCode = 0x12,
                ParameterIndex = 0x27,
                Name = "子机调频状态",
                Description = "子机实际调频状态和保护状态",
                Category = "Status",
                DataType = "u16",
                ValueFormat = ParameterValueFormat.BitField,
                FormatConfiguration = JsonSerializer.Serialize(new BitFieldConfig
                {
                    BitDefinitions = new[]
                    {
                        new BitDefinition { Name = "调频状态", StartBit = 12, BitCount = 4,
                            EnumValues = new[] { "停止", "故障", "残余", "容量分配", "定频待机", "启动时序", "关闭待机", "关闭时序", "不可分配阶段" } },
                        new BitDefinition { Name = "子机能力保护", StartBit = 7, BitCount = 1 },
                        new BitDefinition { Name = "变频压缩机转速保护", StartBit = 6, BitCount = 1 },
                        new BitDefinition { Name = "模块温度过高", StartBit = 5, BitCount = 1 },
                        new BitDefinition { Name = "电流过高", StartBit = 4, BitCount = 1 },
                        new BitDefinition { Name = "Td过高", StartBit = 3, BitCount = 1 },
                        new BitDefinition { Name = "压缩比过高", StartBit = 2, BitCount = 1 },
                        new BitDefinition { Name = "Ps过低", StartBit = 1, BitCount = 1 },
                        new BitDefinition { Name = "Pd过高", StartBit = 0, BitCount = 1 }
                    }
                }),
                Unit = "",
                IsReadOnly = true
            }
        };
    }
}
```

### 7.4 数据存储流程示例

#### 7.4.1 格式1数据存储（参数索引格式）
```csharp
public async Task ProcessFormat1Data(ParseResult parseResult, Device device)
{
    // 使用新的参数解析器
    var parser = new Format1ParameterParser(_parameterRepository);
    var parsedParameters = await parser.ParseParametersAsync(
        parseResult.CommandCode,
        parseResult.Parameters[0].Index,
        parseResult.Parameters);

    foreach (var parsedParam in parsedParameters)
    {
        // 查找或创建设备参数关联
        var deviceParam = await _deviceParameterRepository.GetOrCreateAsync(
            device.Id, parsedParam.Definition.Id);

        // 存储监控数据
        var monitoringData = new MonitoringData
        {
            DeviceId = device.Id,
            DeviceParameterId = deviceParam.Id,
            ParameterIndex = parsedParam.Index,
            RawValue = parsedParam.RawValue,
            ParsedValue = JsonSerializer.Serialize(parsedParam.ParsedValue),
            NumericValue = ExtractNumericValue(parsedParam.ParsedValue),
            StringValue = ExtractStringValue(parsedParam.ParsedValue),
            BooleanValue = ExtractBooleanValue(parsedParam.ParsedValue),
            Quality = DataQuality.Good,
            Timestamp = DateTimeOffset.UtcNow,
            CreatedAt = DateTimeOffset.UtcNow
        };

        await _monitoringRepository.SaveAsync(monitoringData);

        // 记录通信日志
        await _communicationLogRepository.SaveAsync(new CommunicationLog
        {
            DeviceId = device.Id,
            CommunicationType = CommunicationType.PointRead,
            ProtocolFormat = ProtocolFormat.ParameterIndex,
            SourceAddress = parseResult.SourceAddress,
            TargetAddress = parseResult.TargetAddress,
            CommandCode = parseResult.CommandCode,
            RawData = BuildRawDataForParameter(parsedParam),
            ParsedData = JsonSerializer.Serialize(parsedParam.ParsedValue),
            IsSuccessful = true,
            Timestamp = DateTimeOffset.UtcNow
        });
    }
}

// 实际使用示例：
// 解析命令码0x12，参数索引0x35的数据0x014F
// 1. 查询参数定义表：CommandCode=0x12, ParameterIndex=0x35 -> "额定容量"
// 2. 应用解析配置：u16格式，乘数100，单位W
// 3. 计算结果：0x014F = 335，335 * 100 = 33500W = 33.5kW
// 4. 存储到MonitoringData表
```

#### 7.4.2 格式2数据存储（自定义数据格式）
```csharp
public async Task ProcessFormat2Data(ParseResult parseResult, Device device)
{
    // 根据命令码确定数据解析方式
    switch (parseResult.CommandCode)
    {
        case 0xA1: // 系统状态数据
            await ProcessA1SystemStatus(parseResult.Data, device);
            break;
        case 0xA2: // 其他状态数据
            await ProcessA2StatusData(parseResult.Data, device);
            break;
        default:
            // 存储为原始通信日志
            await StoreCommunicationLog(parseResult, device);
            break;
    }
}

private async Task ProcessA1SystemStatus(byte[] customData, Device device)
{
    // 使用格式2数据解析器
    var parser = new Format2DataParser();
    var systemStatus = parser.ParseA1Command(customData);

    // 存储系统标志位数据
    await StoreSystemFlags(device, systemStatus.SystemFlags);

    // 存储室外机状态数据
    await StoreOutdoorStatus(device, systemStatus.OutdoorStatus);

    // 存储温度数据
    await StoreTemperatureData(device, systemStatus);

    // 记录通信日志
    await _communicationLogRepository.SaveAsync(new CommunicationLog
    {
        DeviceId = device.Id,
        CommunicationType = CommunicationType.PointRead,
        ProtocolFormat = ProtocolFormat.CustomData,
        SourceAddress = parseResult.SourceAddress,
        TargetAddress = parseResult.TargetAddress,
        CommandCode = parseResult.CommandCode,
        RawData = customData,
        ParsedData = JsonSerializer.Serialize(systemStatus),
        IsSuccessful = true,
        Timestamp = DateTimeOffset.UtcNow
    });
}

private async Task StoreSystemFlags(Device device, SystemFlags flags)
{
    // 将系统标志位作为特殊参数存储
    var flagsData = new MonitoringData
    {
        DeviceId = device.Id,
        DeviceParameterId = await GetSystemFlagsParameterId(),
        ParameterIndex = 0xFF, // 特殊索引表示系统标志
        RawValue = 0x23, // 原始字节值
        ParsedValue = JsonSerializer.Serialize(flags),
        StringValue = $"Mode:{flags.SystemModePriority},AutoAddr:{flags.AllowAutoAddressing}",
        Quality = DataQuality.Good,
        Timestamp = DateTimeOffset.UtcNow,
        CreatedAt = DateTimeOffset.UtcNow
    };

    await _monitoringRepository.SaveAsync(flagsData);
}

private async Task StoreOutdoorStatus(Device device, OutdoorStatus status)
{
    // 存储室外机工作状态
    var statusData = new MonitoringData
    {
        DeviceId = device.Id,
        DeviceParameterId = await GetOutdoorStatusParameterId(),
        ParameterIndex = 0xFE, // 特殊索引表示室外机状态
        RawValue = 0x60, // 原始字节值
        ParsedValue = JsonSerializer.Serialize(status),
        StringValue = $"Mode:{status.WorkMode},Compressor:{status.CompressorRunning}",
        BooleanValue = status.CompressorRunning,
        Quality = DataQuality.Good,
        Timestamp = DateTimeOffset.UtcNow,
        CreatedAt = DateTimeOffset.UtcNow
    };

    await _monitoringRepository.SaveAsync(statusData);
}

private async Task StoreTemperatureData(Device device, CustomDataResult result)
{
    // 存储高压饱和温度
    await _monitoringRepository.SaveAsync(new MonitoringData
    {
        DeviceId = device.Id,
        DeviceParameterId = await GetHighPressureTempParameterId(),
        ParameterIndex = 0xFD,
        RawValue = 0x021C, // 原始组合值
        ParsedValue = result.HighPressureSaturationTemp.ToString(),
        NumericValue = result.HighPressureSaturationTemp,
        Quality = DataQuality.Good,
        Timestamp = DateTimeOffset.UtcNow,
        CreatedAt = DateTimeOffset.UtcNow
    });

    // 存储低压饱和温度
    await _monitoringRepository.SaveAsync(new MonitoringData
    {
        DeviceId = device.Id,
        DeviceParameterId = await GetLowPressureTempParameterId(),
        ParameterIndex = 0xFC,
        RawValue = 0x001E, // 原始组合值
        ParsedValue = result.LowPressureSaturationTemp.ToString(),
        NumericValue = result.LowPressureSaturationTemp,
        Quality = DataQuality.Good,
        Timestamp = DateTimeOffset.UtcNow,
        CreatedAt = DateTimeOffset.UtcNow
    });
}

// 实际使用示例：
// 解析命令码0xA1的16字节自定义数据
// 1. 第1字节0x23按位域解析为系统标志
// 2. 第2字节0x60按位域解析为室外机状态
// 3. 第3-4字节0x021C组合解析为高压饱和温度53.2°C
// 4. 第5-6字节0x001E组合解析为低压饱和温度3.0°C
// 5. 分别存储到MonitoringData表的不同记录中
```

## 8. 参数值解析策略

### 7.1 参数值格式解析器
```csharp
public interface IParameterValueParser
{
    object ParseValue(ushort rawValue, ParameterDefinition definition);
    ushort EncodeValue(object value, ParameterDefinition definition);
}

public class ParameterValueParserFactory
{
    public IParameterValueParser CreateParser(ParameterValueFormat format)
    {
        return format switch
        {
            ParameterValueFormat.SingleValue => new SingleValueParser(),
            ParameterValueFormat.BitField => new BitFieldParser(),
            ParameterValueFormat.ByteCombination => new ByteCombinationParser(),
            ParameterValueFormat.MixedFormat => new MixedFormatParser(),
            ParameterValueFormat.EnumValue => new EnumValueParser(),
            _ => throw new NotSupportedException($"Unsupported format: {format}")
        };
    }
}
```

### 7.2 解析器实现示例
```csharp
public class SingleValueParser : IParameterValueParser
{
    public object ParseValue(ushort rawValue, ParameterDefinition definition)
    {
        var config = JsonSerializer.Deserialize<SingleValueConfig>(definition.FormatConfiguration ?? "{}");
        return rawValue / (decimal)Math.Pow(10, config.DecimalPlaces);
    }

    public ushort EncodeValue(object value, ParameterDefinition definition)
    {
        var config = JsonSerializer.Deserialize<SingleValueConfig>(definition.FormatConfiguration ?? "{}");
        return (ushort)(Convert.ToDecimal(value) * (decimal)Math.Pow(10, config.DecimalPlaces));
    }
}

public class BitFieldParser : IParameterValueParser
{
    public object ParseValue(ushort rawValue, ParameterDefinition definition)
    {
        var config = JsonSerializer.Deserialize<BitFieldConfig>(definition.FormatConfiguration ?? "{}");
        var result = new Dictionary<string, bool>();

        foreach (var bit in config.BitDefinitions)
        {
            result[bit.Name] = (rawValue & (1 << bit.Position)) != 0;
        }

        return result;
    }

    public ushort EncodeValue(object value, ParameterDefinition definition)
    {
        // 实现位域编码逻辑
        throw new NotImplementedException();
    }
}
```

## 8. 数据库迁移策略

### 8.1 初始迁移
```bash
# 创建初始迁移
dotnet ef migrations add InitialCreate

# 更新数据库
dotnet ef database update
```

### 8.2 种子数据
```csharp
public static class SeedData
{
    public static void Initialize(AirMonitorDbContext context)
    {
        // 创建默认参数模板
        if (!context.ParameterTemplates.Any())
        {
            var outdoorTemplate = new ParameterTemplate
            {
                Name = "标准室外机参数模板",
                Description = "适用于标准室外机的参数定义",
                ApplicableDeviceType = DeviceType.OutdoorUnit,
                ModelPattern = "*",
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            context.ParameterTemplates.Add(outdoorTemplate);
            context.SaveChanges();

            // 添加参数定义
            var parameterDefinitions = new[]
            {
                new ParameterDefinition
                {
                    ParameterTemplateId = outdoorTemplate.Id,
                    ParameterIndex = 0,
                    Name = "环境温度",
                    Description = "室外环境温度",
                    ValueFormat = ParameterValueFormat.SingleValue,
                    FormatConfiguration = JsonSerializer.Serialize(new { DecimalPlaces = 1 }),
                    Unit = "°C",
                    MinValue = -40,
                    MaxValue = 80,
                    IsReadOnly = true,
                    DisplayOrder = 1,
                    IsActive = true
                },
                new ParameterDefinition
                {
                    ParameterTemplateId = outdoorTemplate.Id,
                    ParameterIndex = 1,
                    Name = "运行状态",
                    Description = "设备运行状态位域",
                    ValueFormat = ParameterValueFormat.BitField,
                    FormatConfiguration = JsonSerializer.Serialize(new
                    {
                        BitDefinitions = new[]
                        {
                            new { Name = "运行", Position = 0 },
                            new { Name = "制冷", Position = 1 },
                            new { Name = "制热", Position = 2 },
                            new { Name = "除霜", Position = 3 }
                        }
                    }),
                    Unit = "",
                    IsReadOnly = true,
                    DisplayOrder = 2,
                    IsActive = true
                }
            };

            context.ParameterDefinitions.AddRange(parameterDefinitions);
            context.SaveChanges();
        }
    }
}
```

## 9. 性能优化考虑

### 9.1 索引策略
- 设备地址唯一索引
- 时间戳索引（监控数据表）
- 复合索引（设备ID + 参数索引 + 时间戳）
- 外键索引自动创建

### 9.2 分区策略
```sql
-- 监控数据表按月分区
CREATE PARTITION FUNCTION PF_MonitoringData_Monthly (datetimeoffset)
AS RANGE RIGHT FOR VALUES
('2024-01-01', '2024-02-01', '2024-03-01', ...);
```

### 9.3 缓存策略
- 参数模板和定义缓存
- 设备基本信息缓存
- 最新监控数据缓存

## 10. 安全考虑

### 10.1 数据加密
- 敏感配置信息加密存储
- 通信日志中的敏感数据脱敏

### 10.2 访问控制
- 基于角色的数据访问控制
- 行级安全策略
- 审计日志记录

### 10.3 数据完整性
- 外键约束确保引用完整性
- 检查约束确保数据有效性
- 并发控制防止数据冲突

## 11. 基于角色的参数权限控制

### 11.1 权限控制实体扩展

#### 11.1.1 ParameterPermission (参数权限)
```csharp
public class ParameterPermission
{
    public int Id { get; set; }
    public UserRole UserRole { get; set; }
    public int ParameterDefinitionId { get; set; }
    public PermissionLevel PermissionLevel { get; set; }
    public bool IsActive { get; set; }
    public DateTimeOffset CreatedAt { get; set; }

    // 导航属性
    public virtual ParameterDefinition ParameterDefinition { get; set; } = null!;
}
```

### 11.2 角色权限配置示例

#### 11.2.1 系统管理员权限配置
```csharp
// 系统管理员：完全访问所有参数 + 系统管理功能
public class SystemAdminPermissions
{
    public static readonly string[] ManagementPermissions =
    {
        "User.Create", "User.Update", "User.Delete", "User.View",
        "Role.Assign", "Role.Revoke",
        "Template.Create", "Template.Update", "Template.Delete",
        "Parameter.Configure", "Parameter.Enable", "Parameter.Disable",
        "Device.Configure", "Device.Reset", "Device.Calibrate"
    };

    // 对所有参数具有完全控制权限
    public static PermissionLevel GetParameterPermission(ParameterDefinition parameter)
    {
        return PermissionLevel.FullControl;
    }
}
```

#### 11.2.2 研发人员权限配置
```csharp
// 研发人员：监控所有设备的所有参数
public class RDPersonnelPermissions
{
    // 对所有参数具有读写权限
    public static PermissionLevel GetParameterPermission(ParameterDefinition parameter)
    {
        return PermissionLevel.ReadWrite;
    }

    // 可访问的特殊功能
    public static readonly string[] SpecialPermissions =
    {
        "Debug.Enable", "Debug.ViewRawData", "Debug.SendCommand",
        "Calibration.View", "Calibration.Adjust",
        "Protocol.Monitor", "Protocol.Analyze"
    };
}
```

#### 11.2.3 服务人员权限配置
```csharp
// 服务人员：监控所有设备的部分参数
public class ServicePersonnelPermissions
{
    // 允许访问的参数类别
    public static readonly string[] AllowedParameterCategories =
    {
        "Temperature", "Pressure", "Status", "Alarm", "Maintenance"
    };

    // 禁止访问的参数类别
    public static readonly string[] ForbiddenParameterCategories =
    {
        "Debug", "Calibration", "Factory", "Security"
    };

    public static PermissionLevel GetParameterPermission(ParameterDefinition parameter)
    {
        // 根据参数类别判断权限
        if (ForbiddenParameterCategories.Contains(parameter.Category))
            return PermissionLevel.None;

        if (AllowedParameterCategories.Contains(parameter.Category))
            return PermissionLevel.ReadOnly;

        return PermissionLevel.None;
    }
}
```

#### 11.2.4 普通用户权限配置
```csharp
// 普通用户：监控基础参数（比服务人员更少）
public class RegularUserPermissions
{
    // 只允许访问基础运行参数
    public static readonly string[] AllowedParameterCategories =
    {
        "Temperature", "BasicStatus"
    };

    // 允许访问的具体参数索引
    public static readonly byte[] AllowedParameterIndexes =
    {
        0, 1, 2, 10, 11  // 温度、基础状态等
    };

    public static PermissionLevel GetParameterPermission(ParameterDefinition parameter)
    {
        if (AllowedParameterCategories.Contains(parameter.Category) &&
            AllowedParameterIndexes.Contains(parameter.ParameterIndex))
        {
            return PermissionLevel.ReadOnly;
        }

        return PermissionLevel.None;
    }
}
```

### 11.3 权限验证服务
```csharp
public interface IParameterPermissionService
{
    Task<PermissionLevel> GetParameterPermissionAsync(int userId, int parameterDefinitionId);
    Task<IEnumerable<ParameterDefinition>> GetAccessibleParametersAsync(int userId, DeviceType deviceType);
    Task<bool> CanAccessParameterAsync(int userId, int parameterDefinitionId, PermissionLevel requiredLevel);
}

public class ParameterPermissionService : IParameterPermissionService
{
    private readonly IUserRepository _userRepository;
    private readonly IParameterRepository _parameterRepository;

    public ParameterPermissionService(IUserRepository userRepository, IParameterRepository parameterRepository)
    {
        _userRepository = userRepository;
        _parameterRepository = parameterRepository;
    }

    public async Task<PermissionLevel> GetParameterPermissionAsync(int userId, int parameterDefinitionId)
    {
        var user = await _userRepository.GetByIdAsync(userId);
        var parameter = await _parameterRepository.GetDefinitionByIdAsync(parameterDefinitionId);

        return user.Role switch
        {
            UserRole.SystemAdmin => PermissionLevel.FullControl,
            UserRole.RDPersonnel => PermissionLevel.ReadWrite,
            UserRole.ServicePersonnel => ServicePersonnelPermissions.GetParameterPermission(parameter),
            UserRole.RegularUser => RegularUserPermissions.GetParameterPermission(parameter),
            _ => PermissionLevel.None
        };
    }

    public async Task<IEnumerable<ParameterDefinition>> GetAccessibleParametersAsync(int userId, DeviceType deviceType)
    {
        var allParameters = await _parameterRepository.GetByDeviceTypeAsync(deviceType);
        var accessibleParameters = new List<ParameterDefinition>();

        foreach (var parameter in allParameters)
        {
            var permission = await GetParameterPermissionAsync(userId, parameter.Id);
            if (permission > PermissionLevel.None)
            {
                accessibleParameters.Add(parameter);
            }
        }

        return accessibleParameters;
    }

    public async Task<bool> CanAccessParameterAsync(int userId, int parameterDefinitionId, PermissionLevel requiredLevel)
    {
        var userPermission = await GetParameterPermissionAsync(userId, parameterDefinitionId);
        return userPermission >= requiredLevel;
    }
}
```

### 11.4 API权限控制示例
```csharp
[ApiController]
[Route("api/[controller]")]
public class MonitoringDataController : ControllerBase
{
    private readonly IParameterPermissionService _permissionService;
    private readonly IMonitoringDataRepository _monitoringRepository;

    [HttpGet("device/{deviceId}/parameters")]
    public async Task<IActionResult> GetDeviceParameters(int deviceId)
    {
        var userId = GetCurrentUserId();
        var device = await _deviceRepository.GetByIdAsync(deviceId);

        // 获取用户可访问的参数
        var accessibleParameters = await _permissionService.GetAccessibleParametersAsync(userId, device.DeviceType);

        var result = accessibleParameters.Select(p => new
        {
            p.Id,
            p.Name,
            p.Description,
            p.Unit,
            Permission = _permissionService.GetParameterPermissionAsync(userId, p.Id).Result
        });

        return Ok(result);
    }

    [HttpGet("device/{deviceId}/parameter/{parameterIndex}/latest")]
    public async Task<IActionResult> GetLatestParameterValue(int deviceId, byte parameterIndex)
    {
        var userId = GetCurrentUserId();
        var parameterDef = await _parameterRepository.GetByDeviceAndIndexAsync(deviceId, parameterIndex);

        // 检查读取权限
        if (!await _permissionService.CanAccessParameterAsync(userId, parameterDef.Id, PermissionLevel.ReadOnly))
        {
            return Forbid("您没有权限访问此参数");
        }

        var latestData = await _monitoringRepository.GetLatestByParameterAsync(deviceId, parameterIndex);
        return Ok(latestData);
    }
}
```
