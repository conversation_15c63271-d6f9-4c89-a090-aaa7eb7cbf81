# 数据模型设计开发文档

## 1. 功能需求描述

### 1.1 业务背景
IoT监控系统需要管理通过RS485通信协议连接的空调设备网络，包括室外机、室内机和集中控制器。系统采用主从轮询架构，主设备（通常为外机F1）通过RS485总线轮询各从设备，收集运行参数和状态信息。

### 1.2 功能范围
- **设备管理**: 动态发现和管理RS485总线上的各类设备
- **通信协议支持**: 支持两种RS485数据帧格式的解析和存储
- **参数管理**: 支持参数索引表的动态配置和多格式参数值解析
- **数据存储**: 灵活存储不同设备类型的监控数据
- **权限控制**: 基于角色的设备访问权限管理
- **状态监控**: 设备在线/离线状态跟踪

### 1.3 用户故事
- 作为系统管理员，我需要查看和管理所有RS485设备的连接状态和基本信息
- 作为研发人员，我需要访问所有设备参数进行调试和分析
- 作为服务人员，我需要访问授权设备的特定参数进行维护
- 作为普通用户，我需要查看和控制基本的设备运行参数

## 2. 技术实现方案

### 2.1 架构设计
数据模型采用分层设计，包含以下核心层次：
- **设备层**: 管理物理设备和通信连接
- **协议层**: 处理RS485通信协议和数据帧解析
- **参数层**: 管理参数定义和值存储
- **权限层**: 控制用户对设备和参数的访问权限

### 2.2 技术选型
- **ORM框架**: Entity Framework Core 8.0
- **数据库**: SQL Server / PostgreSQL
- **数据注解**: System.ComponentModel.DataAnnotations
- **JSON序列化**: System.Text.Json
- **时间处理**: DateTimeOffset for UTC timestamps

### 2.3 设计模式
- **仓储模式**: 数据访问抽象
- **工厂模式**: 协议解析器创建
- **策略模式**: 参数值格式解析
- **观察者模式**: 设备状态变更通知

## 3. 数据模型核心实体设计

### 3.1 设备管理实体

#### 3.1.1 AirConditioningUnit (空调机组)
```csharp
public class AirConditioningUnit
{
    public int Id { get; set; }
    public string? SerialNumber { get; set; }
    public DateTimeOffset InstallationDate { get; set; }
    public bool IsActive { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    public virtual ICollection<Device> Devices { get; set; } = new List<Device>();
}
```

#### 3.1.2 Device (设备)
```csharp
public class Device
{
    public int Id { get; set; }
    public byte DeviceAddress { get; set; }
    public DeviceType DeviceType { get; set; }
    public string? SerialNumber { get; set; }
    public string ModelNumber { get; set; } = string.Empty;
    public string FirmwareVersion { get; set; } = string.Empty;
    public DeviceStatus Status { get; set; }
    public DateTimeOffset LastCommunication { get; set; }
    public bool IsOnline { get; set; }
    public int AirConditioningUnitId { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    public virtual AirConditioningUnit AirConditioningUnit { get; set; } = null!;
    public virtual ICollection<DeviceParameter> DeviceParameters { get; set; } = new List<DeviceParameter>();
    public virtual ICollection<CommunicationLog> CommunicationLogs { get; set; } = new List<CommunicationLog>();
    public virtual ICollection<MonitoringData> MonitoringData { get; set; } = new List<MonitoringData>();
}
```

### 3.2 通信协议实体

#### 3.2.1 CommunicationLog (通信日志)
```csharp
public class CommunicationLog
{
    public long Id { get; set; }
    public int DeviceId { get; set; }
    public CommunicationType CommunicationType { get; set; }
    public ProtocolFormat ProtocolFormat { get; set; }
    public byte SourceAddress { get; set; }
    public byte TargetAddress { get; set; }
    public byte CommandCode { get; set; }
    public byte[] RawData { get; set; } = Array.Empty<byte>();
    public string? ParsedData { get; set; }
    public bool IsSuccessful { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    
    // 导航属性
    public virtual Device Device { get; set; } = null!;
}
```

### 3.3 参数管理实体

#### 3.3.1 ParameterTemplate (参数模板)
```csharp
public class ParameterTemplate
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DeviceType ApplicableDeviceType { get; set; }
    public string ModelPattern { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    public virtual ICollection<ParameterDefinition> ParameterDefinitions { get; set; } = new List<ParameterDefinition>();
}
```

#### 3.3.2 ParameterDefinition (参数定义)
```csharp
public class ParameterDefinition
{
    public int Id { get; set; }
    public int ParameterTemplateId { get; set; }
    public byte ParameterIndex { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ParameterValueFormat ValueFormat { get; set; }
    public string? FormatConfiguration { get; set; }
    public string Unit { get; set; } = string.Empty;
    public decimal? MinValue { get; set; }
    public decimal? MaxValue { get; set; }
    public bool IsReadOnly { get; set; }
    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; }
    
    // 导航属性
    public virtual ParameterTemplate ParameterTemplate { get; set; } = null!;
    public virtual ICollection<DeviceParameter> DeviceParameters { get; set; } = new List<DeviceParameter>();
}
```

#### 3.3.3 DeviceParameter (设备参数)
```csharp
public class DeviceParameter
{
    public int Id { get; set; }
    public int DeviceId { get; set; }
    public int ParameterDefinitionId { get; set; }
    public bool IsEnabled { get; set; }
    public string? CustomName { get; set; }
    public string? CustomConfiguration { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }

    // 导航属性
    public virtual Device Device { get; set; } = null!;
    public virtual ParameterDefinition ParameterDefinition { get; set; } = null!;
    public virtual ICollection<MonitoringData> MonitoringData { get; set; } = new List<MonitoringData>();
}
```

### 3.4 监控数据实体

#### 3.4.1 MonitoringData (监控数据)
```csharp
public class MonitoringData
{
    public long Id { get; set; }
    public int DeviceId { get; set; }
    public int DeviceParameterId { get; set; }
    public byte ParameterIndex { get; set; }
    public ushort RawValue { get; set; }
    public string? ParsedValue { get; set; }
    public decimal? NumericValue { get; set; }
    public string? StringValue { get; set; }
    public bool? BooleanValue { get; set; }
    public DataQuality Quality { get; set; }
    public DateTimeOffset Timestamp { get; set; }
    public DateTimeOffset CreatedAt { get; set; }

    // 导航属性
    public virtual Device Device { get; set; } = null!;
    public virtual DeviceParameter DeviceParameter { get; set; } = null!;
}
```

### 3.5 权限管理实体

#### 3.5.1 User (用户)
```csharp
public class User
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string PasswordHash { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset UpdatedAt { get; set; }
    public DateTimeOffset? LastLoginAt { get; set; }

    // 导航属性
    public virtual ICollection<UserDevicePermission> DevicePermissions { get; set; } = new List<UserDevicePermission>();
}
```

#### 3.5.2 UserDevicePermission (用户设备权限)
```csharp
public class UserDevicePermission
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int DeviceId { get; set; }
    public PermissionLevel PermissionLevel { get; set; }
    public DateTimeOffset GrantedAt { get; set; }
    public DateTimeOffset? ExpiresAt { get; set; }
    public bool IsActive { get; set; }

    // 导航属性
    public virtual User User { get; set; } = null!;
    public virtual Device Device { get; set; } = null!;
}
```

## 4. 枚举类型定义

### 4.1 设备相关枚举
```csharp
public enum DeviceType
{
    OutdoorUnit = 1,    // 室外机
    IndoorUnit = 2,     // 室内机
    Controller = 3      // 集中控制器
}

public enum DeviceStatus
{
    Unknown = 0,        // 未知状态
    Normal = 1,         // 正常运行
    Warning = 2,        // 警告状态
    Error = 3,          // 错误状态
    Maintenance = 4     // 维护状态
}
```

### 4.2 通信相关枚举
```csharp
public enum CommunicationType
{
    PointRead = 1,      // 点播读取
    ControlCommand = 2  // 控制命令
}

public enum ProtocolFormat
{
    ParameterIndex = 1, // 参数索引格式（变长）
    CustomData = 2      // 自定义数据格式（定长）
}
```

### 4.3 参数相关枚举
```csharp
public enum ParameterValueFormat
{
    SingleValue = 1,    // 单一参数值
    BitField = 2,       // 位域组合
    ByteCombination = 3, // 字节组合
    MixedFormat = 4,    // 混合格式
    EnumValue = 5       // 枚举值
}

public enum DataQuality
{
    Good = 1,           // 数据良好
    Uncertain = 2,      // 数据不确定
    Bad = 3             // 数据错误
}
```

### 4.4 权限相关枚举
```csharp
public enum UserRole
{
    SystemAdmin = 1,    // 系统管理员
    RDPersonnel = 2,    // 研发人员
    ServicePersonnel = 3, // 服务人员
    RegularUser = 4     // 普通用户
}

public enum PermissionLevel
{
    None = 0,           // 无权限
    ReadOnly = 1,       // 只读
    ReadWrite = 2,      // 读写
    FullControl = 3     // 完全控制
}
```

## 5. DbContext 配置

### 5.1 AirMonitorDbContext
```csharp
public class AirMonitorDbContext : DbContext
{
    public AirMonitorDbContext(DbContextOptions<AirMonitorDbContext> options) : base(options) { }

    // DbSet 定义
    public DbSet<AirConditioningUnit> AirConditioningUnits { get; set; }
    public DbSet<Device> Devices { get; set; }
    public DbSet<ParameterTemplate> ParameterTemplates { get; set; }
    public DbSet<ParameterDefinition> ParameterDefinitions { get; set; }
    public DbSet<DeviceParameter> DeviceParameters { get; set; }
    public DbSet<MonitoringData> MonitoringData { get; set; }
    public DbSet<CommunicationLog> CommunicationLogs { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserDevicePermission> UserDevicePermissions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 应用所有配置
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(AirMonitorDbContext).Assembly);

        // 全局查询过滤器
        modelBuilder.Entity<AirConditioningUnit>().HasQueryFilter(x => x.IsActive);
        modelBuilder.Entity<Device>().HasQueryFilter(x => x.AirConditioningUnit.IsActive);
        modelBuilder.Entity<User>().HasQueryFilter(x => x.IsActive);
    }
}
```

### 5.2 实体配置示例

#### 5.2.1 DeviceConfiguration
```csharp
public class DeviceConfiguration : IEntityTypeConfiguration<Device>
{
    public void Configure(EntityTypeBuilder<Device> builder)
    {
        builder.ToTable("Devices");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.DeviceAddress)
            .IsRequired()
            .HasComment("RS485设备地址");

        builder.Property(x => x.ModelNumber)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.FirmwareVersion)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.SerialNumber)
            .HasMaxLength(100);

        // 索引
        builder.HasIndex(x => x.DeviceAddress)
            .IsUnique()
            .HasDatabaseName("IX_Devices_DeviceAddress");

        builder.HasIndex(x => x.SerialNumber)
            .HasDatabaseName("IX_Devices_SerialNumber");

        // 关系配置
        builder.HasOne(x => x.AirConditioningUnit)
            .WithMany(x => x.Devices)
            .HasForeignKey(x => x.AirConditioningUnitId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
```

## 6. 数据访问层设计

### 6.1 仓储接口定义
```csharp
public interface IDeviceRepository
{
    Task<Device?> GetByAddressAsync(byte deviceAddress);
    Task<IEnumerable<Device>> GetOnlineDevicesAsync();
    Task<IEnumerable<Device>> GetByTypeAsync(DeviceType deviceType);
    Task UpdateOnlineStatusAsync(byte deviceAddress, bool isOnline);
    Task<Device> CreateAsync(Device device);
    Task<Device> UpdateAsync(Device device);
}

public interface IMonitoringDataRepository
{
    Task<MonitoringData> SaveAsync(MonitoringData data);
    Task<IEnumerable<MonitoringData>> GetLatestByDeviceAsync(int deviceId, int count = 100);
    Task<IEnumerable<MonitoringData>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end);
    Task<decimal?> GetLatestNumericValueAsync(int deviceId, byte parameterIndex);
}

public interface IParameterTemplateRepository
{
    Task<ParameterTemplate?> GetByDeviceTypeAndModelAsync(DeviceType deviceType, string modelPattern);
    Task<IEnumerable<ParameterDefinition>> GetParameterDefinitionsAsync(int templateId);
    Task<ParameterDefinition?> GetParameterDefinitionAsync(int templateId, byte parameterIndex);
}
```

### 6.2 协议解析服务接口
```csharp
public interface IProtocolParserService
{
    ParseResult ParseFrame(byte[] rawData);
    byte[] BuildFrame(byte sourceAddress, byte targetAddress, byte commandCode, byte[] data);
    bool ValidateCRC(byte[] frame);
}

public class ParseResult
{
    public bool IsValid { get; set; }
    public ProtocolFormat Format { get; set; }
    public byte SourceAddress { get; set; }
    public byte TargetAddress { get; set; }
    public byte CommandCode { get; set; }
    public byte[]? Data { get; set; }
    public ParameterData[]? Parameters { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ParameterData
{
    public byte Index { get; set; }
    public ushort RawValue { get; set; }
    public object? ParsedValue { get; set; }
}
```

## 7. 参数值解析策略

### 7.1 参数值格式解析器
```csharp
public interface IParameterValueParser
{
    object ParseValue(ushort rawValue, ParameterDefinition definition);
    ushort EncodeValue(object value, ParameterDefinition definition);
}

public class ParameterValueParserFactory
{
    public IParameterValueParser CreateParser(ParameterValueFormat format)
    {
        return format switch
        {
            ParameterValueFormat.SingleValue => new SingleValueParser(),
            ParameterValueFormat.BitField => new BitFieldParser(),
            ParameterValueFormat.ByteCombination => new ByteCombinationParser(),
            ParameterValueFormat.MixedFormat => new MixedFormatParser(),
            ParameterValueFormat.EnumValue => new EnumValueParser(),
            _ => throw new NotSupportedException($"Unsupported format: {format}")
        };
    }
}
```

### 7.2 解析器实现示例
```csharp
public class SingleValueParser : IParameterValueParser
{
    public object ParseValue(ushort rawValue, ParameterDefinition definition)
    {
        var config = JsonSerializer.Deserialize<SingleValueConfig>(definition.FormatConfiguration ?? "{}");
        return rawValue / (decimal)Math.Pow(10, config.DecimalPlaces);
    }

    public ushort EncodeValue(object value, ParameterDefinition definition)
    {
        var config = JsonSerializer.Deserialize<SingleValueConfig>(definition.FormatConfiguration ?? "{}");
        return (ushort)(Convert.ToDecimal(value) * (decimal)Math.Pow(10, config.DecimalPlaces));
    }
}

public class BitFieldParser : IParameterValueParser
{
    public object ParseValue(ushort rawValue, ParameterDefinition definition)
    {
        var config = JsonSerializer.Deserialize<BitFieldConfig>(definition.FormatConfiguration ?? "{}");
        var result = new Dictionary<string, bool>();

        foreach (var bit in config.BitDefinitions)
        {
            result[bit.Name] = (rawValue & (1 << bit.Position)) != 0;
        }

        return result;
    }

    public ushort EncodeValue(object value, ParameterDefinition definition)
    {
        // 实现位域编码逻辑
        throw new NotImplementedException();
    }
}
```

## 8. 数据库迁移策略

### 8.1 初始迁移
```bash
# 创建初始迁移
dotnet ef migrations add InitialCreate

# 更新数据库
dotnet ef database update
```

### 8.2 种子数据
```csharp
public static class SeedData
{
    public static void Initialize(AirMonitorDbContext context)
    {
        // 创建默认参数模板
        if (!context.ParameterTemplates.Any())
        {
            var outdoorTemplate = new ParameterTemplate
            {
                Name = "标准室外机参数模板",
                Description = "适用于标准室外机的参数定义",
                ApplicableDeviceType = DeviceType.OutdoorUnit,
                ModelPattern = "*",
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            context.ParameterTemplates.Add(outdoorTemplate);
            context.SaveChanges();

            // 添加参数定义
            var parameterDefinitions = new[]
            {
                new ParameterDefinition
                {
                    ParameterTemplateId = outdoorTemplate.Id,
                    ParameterIndex = 0,
                    Name = "环境温度",
                    Description = "室外环境温度",
                    ValueFormat = ParameterValueFormat.SingleValue,
                    FormatConfiguration = JsonSerializer.Serialize(new { DecimalPlaces = 1 }),
                    Unit = "°C",
                    MinValue = -40,
                    MaxValue = 80,
                    IsReadOnly = true,
                    DisplayOrder = 1,
                    IsActive = true
                },
                new ParameterDefinition
                {
                    ParameterTemplateId = outdoorTemplate.Id,
                    ParameterIndex = 1,
                    Name = "运行状态",
                    Description = "设备运行状态位域",
                    ValueFormat = ParameterValueFormat.BitField,
                    FormatConfiguration = JsonSerializer.Serialize(new
                    {
                        BitDefinitions = new[]
                        {
                            new { Name = "运行", Position = 0 },
                            new { Name = "制冷", Position = 1 },
                            new { Name = "制热", Position = 2 },
                            new { Name = "除霜", Position = 3 }
                        }
                    }),
                    Unit = "",
                    IsReadOnly = true,
                    DisplayOrder = 2,
                    IsActive = true
                }
            };

            context.ParameterDefinitions.AddRange(parameterDefinitions);
            context.SaveChanges();
        }
    }
}
```

## 9. 性能优化考虑

### 9.1 索引策略
- 设备地址唯一索引
- 时间戳索引（监控数据表）
- 复合索引（设备ID + 参数索引 + 时间戳）
- 外键索引自动创建

### 9.2 分区策略
```sql
-- 监控数据表按月分区
CREATE PARTITION FUNCTION PF_MonitoringData_Monthly (datetimeoffset)
AS RANGE RIGHT FOR VALUES
('2024-01-01', '2024-02-01', '2024-03-01', ...);
```

### 9.3 缓存策略
- 参数模板和定义缓存
- 设备基本信息缓存
- 最新监控数据缓存

## 10. 安全考虑

### 10.1 数据加密
- 敏感配置信息加密存储
- 通信日志中的敏感数据脱敏

### 10.2 访问控制
- 基于角色的数据访问控制
- 行级安全策略
- 审计日志记录

### 10.3 数据完整性
- 外键约束确保引用完整性
- 检查约束确保数据有效性
- 并发控制防止数据冲突
```
```
```
