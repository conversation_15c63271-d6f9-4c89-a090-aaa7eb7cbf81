using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Reflection;
using AirMonitor.Infrastructure.Configuration;

namespace AirMonitor.Infrastructure.HealthChecks;

/// <summary>
/// 应用程序健康检查
/// </summary>
public class ApplicationHealthCheck : IHealthCheck
{
    private readonly AppSettings _appSettings;
    private static readonly DateTime _startTime = DateTime.UtcNow;

    public ApplicationHealthCheck(IOptions<AppSettings> appSettings)
    {
        _appSettings = appSettings.Value;
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    /// <param name="context">健康检查上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康检查结果</returns>
    public Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var currentProcess = Process.GetCurrentProcess();
            var uptime = DateTime.UtcNow - _startTime;
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version?.ToString() ?? "Unknown";

            var data = new Dictionary<string, object>
            {
                ["applicationName"] = _appSettings.Name,
                ["version"] = version,
                ["environment"] = _appSettings.Environment,
                ["uptime"] = uptime.ToString(@"dd\.hh\:mm\:ss"),
                ["startTime"] = _startTime,
                ["currentTime"] = DateTime.UtcNow,
                ["processId"] = currentProcess.Id,
                ["workingSet"] = FormatBytes(currentProcess.WorkingSet64),
                ["privateMemory"] = FormatBytes(currentProcess.PrivateMemorySize64),
                ["virtualMemory"] = FormatBytes(currentProcess.VirtualMemorySize64),
                ["gcMemory"] = FormatBytes(GC.GetTotalMemory(false)),
                ["threadCount"] = currentProcess.Threads.Count,
                ["handleCount"] = currentProcess.HandleCount
            };

            // 检查内存使用情况
            var memoryUsageMB = currentProcess.WorkingSet64 / 1024 / 1024;
            var memoryThresholdMB = 1000; // 1GB 阈值

            if (memoryUsageMB > memoryThresholdMB)
            {
                return Task.FromResult(HealthCheckResult.Degraded(
                    $"应用程序内存使用过高: {memoryUsageMB}MB (阈值: {memoryThresholdMB}MB)",
                    data: data));
            }

            // 检查运行时间
            if (uptime.TotalDays > 30)
            {
                data["warning"] = "应用程序已运行超过30天，建议重启";
            }

            // 检查垃圾回收情况
            var gen0Collections = GC.CollectionCount(0);
            var gen1Collections = GC.CollectionCount(1);
            var gen2Collections = GC.CollectionCount(2);

            data["gcCollections"] = new
            {
                generation0 = gen0Collections,
                generation1 = gen1Collections,
                generation2 = gen2Collections
            };

            // 检查线程数
            var threadThreshold = 100;
            if (currentProcess.Threads.Count > threadThreshold)
            {
                return Task.FromResult(HealthCheckResult.Degraded(
                    $"线程数过多: {currentProcess.Threads.Count} (阈值: {threadThreshold})",
                    data: data));
            }

            return Task.FromResult(HealthCheckResult.Healthy(
                $"应用程序运行正常，已运行 {uptime:dd\\.hh\\:mm\\:ss}",
                data: data));
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy(
                "应用程序健康检查失败",
                ex,
                new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["applicationName"] = _appSettings.Name
                }));
        }
    }

    /// <summary>
    /// 格式化字节数
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <returns>格式化后的字符串</returns>
    private static string FormatBytes(long bytes)
    {
        const long kb = 1024;
        const long mb = kb * 1024;
        const long gb = mb * 1024;

        return bytes switch
        {
            >= gb => $"{bytes / gb:F2} GB",
            >= mb => $"{bytes / mb:F2} MB",
            >= kb => $"{bytes / kb:F2} KB",
            _ => $"{bytes} B"
        };
    }
}
