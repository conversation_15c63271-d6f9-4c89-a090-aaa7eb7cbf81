using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Entities.Monitoring;
using AirMonitor.Core.Entities.Users;

namespace AirMonitor.Infrastructure.Data;

/// <summary>
/// 空调监控系统数据库上下文
/// </summary>
public class AirMonitorDbContext : DbContext
{
    public AirMonitorDbContext(DbContextOptions<AirMonitorDbContext> options) : base(options)
    {
    }

    // DbSet 定义
    
    /// <summary>
    /// 设备
    /// </summary>
    public DbSet<Device> Devices { get; set; }
    
    /// <summary>
    /// 参数模板
    /// </summary>
    public DbSet<ParameterTemplate> ParameterTemplates { get; set; }
    
    /// <summary>
    /// 参数定义
    /// </summary>
    public DbSet<ParameterDefinition> ParameterDefinitions { get; set; }
    
    /// <summary>
    /// 设备参数
    /// </summary>
    public DbSet<DeviceParameter> DeviceParameters { get; set; }
    
    /// <summary>
    /// 监控数据
    /// </summary>
    public DbSet<MonitoringData> MonitoringData { get; set; }
    
    /// <summary>
    /// 通信日志
    /// </summary>
    public DbSet<CommunicationLog> CommunicationLogs { get; set; }
    
    /// <summary>
    /// 用户
    /// </summary>
    public DbSet<User> Users { get; set; }
    
    /// <summary>
    /// 用户设备权限
    /// </summary>
    public DbSet<UserDevicePermission> UserDevicePermissions { get; set; }
    
    /// <summary>
    /// 参数权限
    /// </summary>
    public DbSet<ParameterPermission> ParameterPermissions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // 应用所有配置
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(AirMonitorDbContext).Assembly);
        
        // 全局查询过滤器
        modelBuilder.Entity<User>().HasQueryFilter(x => x.IsActive);
        
        // 设置表名前缀
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var tableName = entityType.GetTableName();
            if (!string.IsNullOrEmpty(tableName))
            {
                entityType.SetTableName($"AM_{tableName}");
            }
        }
    }
}
