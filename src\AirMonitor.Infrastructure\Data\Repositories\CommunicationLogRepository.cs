using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Monitoring;
using AirMonitor.Core.Interfaces;

namespace AirMonitor.Infrastructure.Data.Repositories;

/// <summary>
/// 通信日志仓储实现
/// </summary>
public class CommunicationLogRepository : ICommunicationLogRepository
{
    private readonly AirMonitorDbContext _context;

    public CommunicationLogRepository(AirMonitorDbContext context)
    {
        _context = context;
    }

    public async Task<CommunicationLog> SaveAsync(CommunicationLog log)
    {
        _context.CommunicationLogs.Add(log);
        await _context.SaveChangesAsync();
        return log;
    }

    public async Task<IEnumerable<CommunicationLog>> GetByDeviceAsync(int deviceId, int count = 100)
    {
        return await _context.CommunicationLogs
            .Where(c => c.DeviceId == deviceId)
            .OrderByDescending(c => c.Timestamp)
            .Take(count)
            .ToListAsync();
    }

    public async Task<IEnumerable<CommunicationLog>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end)
    {
        return await _context.CommunicationLogs
            .Where(c => c.DeviceId == deviceId && c.Timestamp >= start && c.Timestamp <= end)
            .OrderBy(c => c.Timestamp)
            .ToListAsync();
    }

    public async Task<IEnumerable<CommunicationLog>> GetLatestAsync(int count = 100)
    {
        return await _context.CommunicationLogs
            .Include(c => c.Device)
            .OrderByDescending(c => c.Timestamp)
            .Take(count)
            .ToListAsync();
    }
}
