using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Users;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 用户设备权限实体配置
/// </summary>
public class UserDevicePermissionConfiguration : IEntityTypeConfiguration<UserDevicePermission>
{
    public void Configure(EntityTypeBuilder<UserDevicePermission> builder)
    {
        builder.ToTable("UserDevicePermissions");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.PermissionLevel)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("权限级别");
        
        // 索引
        builder.HasIndex(x => new { x.UserId, x.DeviceId })
            .IsUnique()
            .HasDatabaseName("IX_UserDevicePermissions_User_Device");
            
        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("IX_UserDevicePermissions_IsActive");
            
        builder.HasIndex(x => x.ExpiresAt)
            .HasDatabaseName("IX_UserDevicePermissions_ExpiresAt");
        
        // 关系配置
        builder.HasOne(x => x.User)
            .WithMany(x => x.DevicePermissions)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
            
        builder.HasOne(x => x.Device)
            .WithMany(x => x.UserPermissions)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
