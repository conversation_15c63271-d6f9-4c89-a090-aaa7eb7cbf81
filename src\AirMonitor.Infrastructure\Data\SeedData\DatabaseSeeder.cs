using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Entities.Users;
using AirMonitor.Core.Enums;

namespace AirMonitor.Infrastructure.Data.SeedData;

/// <summary>
/// 数据库种子数据初始化器
/// </summary>
public static class DatabaseSeeder
{
    /// <summary>
    /// 初始化种子数据
    /// </summary>
    /// <param name="context">数据库上下文</param>
    public static async Task SeedAsync(AirMonitorDbContext context)
    {
        // 确保数据库已创建
        await context.Database.EnsureCreatedAsync();

        // 种子数据初始化
        await SeedParameterTemplatesAsync(context);
        await SeedUsersAsync(context);
        
        await context.SaveChangesAsync();
    }

    /// <summary>
    /// 初始化参数模板和参数定义
    /// </summary>
    /// <param name="context">数据库上下文</param>
    private static async Task SeedParameterTemplatesAsync(AirMonitorDbContext context)
    {
        if (await context.ParameterTemplates.AnyAsync())
        {
            return; // 已有数据，跳过初始化
        }

        // 创建室外机参数模板
        var outdoorTemplate = new ParameterTemplate
        {
            Name = "标准室外机参数模板",
            Description = "适用于标准室外机的参数定义",
            ApplicableDeviceType = DeviceType.OutdoorUnit,
            ModelPattern = "*",
            IsActive = true,
            CreatedAt = DateTimeOffset.UtcNow,
            UpdatedAt = DateTimeOffset.UtcNow
        };

        context.ParameterTemplates.Add(outdoorTemplate);
        await context.SaveChangesAsync();

        // 添加格式1参数定义
        var format1Parameters = ParameterDefinitionSeedData.GetFormat1Parameters(outdoorTemplate.Id);
        context.ParameterDefinitions.AddRange(format1Parameters);

        // 添加格式2参数定义
        var format2Parameters = ParameterDefinitionSeedData.GetFormat2Parameters(outdoorTemplate.Id);
        context.ParameterDefinitions.AddRange(format2Parameters);

        // 创建室内机参数模板
        var indoorTemplate = new ParameterTemplate
        {
            Name = "标准室内机参数模板",
            Description = "适用于标准室内机的参数定义",
            ApplicableDeviceType = DeviceType.IndoorUnit,
            ModelPattern = "*",
            IsActive = true,
            CreatedAt = DateTimeOffset.UtcNow,
            UpdatedAt = DateTimeOffset.UtcNow
        };

        context.ParameterTemplates.Add(indoorTemplate);
        await context.SaveChangesAsync();

        // 为室内机添加基础参数定义
        var indoorParameters = new[]
        {
            new ParameterDefinition
            {
                ParameterTemplateId = indoorTemplate.Id,
                CommandCode = 0x12,
                ParameterIndex = 0x01,
                Name = "室内温度",
                Description = "室内环境温度",
                Category = "Temperature",
                DataType = "s16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = System.Text.Json.JsonSerializer.Serialize(new 
                { 
                    DataType = "s16", 
                    Multiplier = 0.1, 
                    DecimalPlaces = 1 
                }),
                Unit = "°C",
                IsReadOnly = true,
                DisplayOrder = 1,
                IsActive = true
            },
            new ParameterDefinition
            {
                ParameterTemplateId = indoorTemplate.Id,
                CommandCode = 0x12,
                ParameterIndex = 0x02,
                Name = "设定温度",
                Description = "用户设定的目标温度",
                Category = "Temperature",
                DataType = "s16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = System.Text.Json.JsonSerializer.Serialize(new 
                { 
                    DataType = "s16", 
                    Multiplier = 0.1, 
                    DecimalPlaces = 1 
                }),
                Unit = "°C",
                MinValue = 16,
                MaxValue = 30,
                IsReadOnly = false,
                DisplayOrder = 2,
                IsActive = true
            }
        };

        context.ParameterDefinitions.AddRange(indoorParameters);
    }

    /// <summary>
    /// 初始化用户数据
    /// </summary>
    /// <param name="context">数据库上下文</param>
    private static async Task SeedUsersAsync(AirMonitorDbContext context)
    {
        if (await context.Users.AnyAsync())
        {
            return; // 已有用户数据，跳过初始化
        }

        var users = new[]
        {
            new User
            {
                Username = "admin",
                Email = "<EMAIL>",
                PasswordHash = "AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==", // 默认密码: admin123
                Role = UserRole.SystemAdmin,
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            },
            new User
            {
                Username = "rd_user",
                Email = "<EMAIL>",
                PasswordHash = "AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==", // 默认密码: admin123
                Role = UserRole.RDPersonnel,
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            },
            new User
            {
                Username = "service_user",
                Email = "<EMAIL>",
                PasswordHash = "AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==", // 默认密码: admin123
                Role = UserRole.ServicePersonnel,
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            },
            new User
            {
                Username = "regular_user",
                Email = "<EMAIL>",
                PasswordHash = "AQAAAAEAACcQAAAAEK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8VJBjZhwK8==", // 默认密码: admin123
                Role = UserRole.RegularUser,
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            }
        };

        context.Users.AddRange(users);
    }
}
