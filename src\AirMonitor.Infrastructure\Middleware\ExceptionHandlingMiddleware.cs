using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace AirMonitor.Infrastructure.Middleware;

/// <summary>
/// 全局异常处理中间件
/// </summary>
public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    /// <summary>
    /// 处理请求
    /// </summary>
    /// <param name="context">HTTP 上下文</param>
    /// <returns>任务</returns>
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理请求时发生未处理的异常: {RequestPath}", context.Request.Path);
            await HandleExceptionAsync(context, ex);
        }
    }

    /// <summary>
    /// 处理异常
    /// </summary>
    /// <param name="context">HTTP 上下文</param>
    /// <param name="exception">异常</param>
    /// <returns>任务</returns>
    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = exception switch
        {
            ValidationException validationEx => CreateErrorResponse(
                HttpStatusCode.BadRequest,
                "验证失败",
                validationEx.Message,
                validationEx.Errors),

            NotFoundException notFoundEx => CreateErrorResponse(
                HttpStatusCode.NotFound,
                "资源未找到",
                notFoundEx.Message),

            UnauthorizedException unauthorizedEx => CreateErrorResponse(
                HttpStatusCode.Unauthorized,
                "未授权访问",
                unauthorizedEx.Message),

            ForbiddenException forbiddenEx => CreateErrorResponse(
                HttpStatusCode.Forbidden,
                "访问被禁止",
                forbiddenEx.Message),

            ConflictException conflictEx => CreateErrorResponse(
                HttpStatusCode.Conflict,
                "资源冲突",
                conflictEx.Message),

            TimeoutException timeoutEx => CreateErrorResponse(
                HttpStatusCode.RequestTimeout,
                "请求超时",
                timeoutEx.Message),

            _ => CreateErrorResponse(
                HttpStatusCode.InternalServerError,
                "服务器内部错误",
                "系统发生未知错误，请稍后重试")
        };

        context.Response.StatusCode = (int)response.StatusCode;

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    /// <summary>
    /// 创建错误响应
    /// </summary>
    /// <param name="statusCode">状态码</param>
    /// <param name="title">标题</param>
    /// <param name="detail">详细信息</param>
    /// <param name="errors">错误列表</param>
    /// <returns>错误响应</returns>
    private static ErrorResponse CreateErrorResponse(
        HttpStatusCode statusCode,
        string title,
        string detail,
        IDictionary<string, string[]>? errors = null)
    {
        return new ErrorResponse
        {
            StatusCode = statusCode,
            Title = title,
            Detail = detail,
            Timestamp = DateTime.UtcNow,
            Errors = errors
        };
    }
}

/// <summary>
/// 错误响应模型
/// </summary>
public class ErrorResponse
{
    /// <summary>
    /// 状态码
    /// </summary>
    public HttpStatusCode StatusCode { get; set; }

    /// <summary>
    /// 错误标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 错误详细信息
    /// </summary>
    public string Detail { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public IDictionary<string, string[]>? Errors { get; set; }
}

/// <summary>
/// 验证异常
/// </summary>
public class ValidationException : Exception
{
    public IDictionary<string, string[]> Errors { get; }

    public ValidationException(string message) : base(message)
    {
        Errors = new Dictionary<string, string[]>();
    }

    public ValidationException(string message, IDictionary<string, string[]> errors) : base(message)
    {
        Errors = errors;
    }
}

/// <summary>
/// 资源未找到异常
/// </summary>
public class NotFoundException : Exception
{
    public NotFoundException(string message) : base(message) { }
    public NotFoundException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 未授权异常
/// </summary>
public class UnauthorizedException : Exception
{
    public UnauthorizedException(string message) : base(message) { }
    public UnauthorizedException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 禁止访问异常
/// </summary>
public class ForbiddenException : Exception
{
    public ForbiddenException(string message) : base(message) { }
    public ForbiddenException(string message, Exception innerException) : base(message, innerException) { }
}

/// <summary>
/// 资源冲突异常
/// </summary>
public class ConflictException : Exception
{
    public ConflictException(string message) : base(message) { }
    public ConflictException(string message, Exception innerException) : base(message, innerException) { }
}
