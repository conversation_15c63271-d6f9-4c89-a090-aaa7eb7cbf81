using System.Text.Json;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Enums;

namespace AirMonitor.Infrastructure.Data.SeedData;

/// <summary>
/// 参数定义种子数据
/// </summary>
public static class ParameterDefinitionSeedData
{
    /// <summary>
    /// 获取格式1参数定义种子数据
    /// </summary>
    /// <param name="templateId">参数模板ID</param>
    /// <returns>参数定义数组</returns>
    public static ParameterDefinition[] GetFormat1Parameters(int templateId)
    {
        return new[]
        {
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0x12,
                ParameterIndex = 0x35,
                Name = "额定容量",
                Description = "设备额定制冷/制热容量",
                Category = "Capacity",
                DataType = "u16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = JsonSerializer.Serialize(new 
                { 
                    DataType = "u16", 
                    Multiplier = 100, 
                    DecimalPlaces = 1 
                }),
                Unit = "W",
                IsReadOnly = true,
                DisplayOrder = 1,
                IsActive = true
            },
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0x12,
                ParameterIndex = 0x36,
                Name = "板换气进温度Tgi",
                Description = "板式换热器气体进口温度",
                Category = "Temperature",
                DataType = "s16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = JsonSerializer.Serialize(new 
                { 
                    DataType = "s16", 
                    Multiplier = 0.1, 
                    DecimalPlaces = 1 
                }),
                Unit = "°C",
                IsReadOnly = true,
                DisplayOrder = 2,
                IsActive = true
            },
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0x12,
                ParameterIndex = 0x27,
                Name = "子机调频状态",
                Description = "子机实际调频状态和保护状态",
                Category = "Status",
                DataType = "u16",
                ValueFormat = ParameterValueFormat.BitField,
                FormatConfiguration = JsonSerializer.Serialize(new
                {
                    BitDefinitions = new object[]
                    {
                        new { Name = "调频状态", StartBit = 12, BitCount = 4,
                            EnumValues = new[] { "停止", "故障", "残余", "容量分配", "定频待机", "启动时序", "关闭待机", "关闭时序", "不可分配阶段" } },
                        new { Name = "子机能力保护", StartBit = 7, BitCount = 1 },
                        new { Name = "变频压缩机转速保护", StartBit = 6, BitCount = 1 },
                        new { Name = "模块温度过高", StartBit = 5, BitCount = 1 },
                        new { Name = "电流过高", StartBit = 4, BitCount = 1 },
                        new { Name = "Td过高", StartBit = 3, BitCount = 1 },
                        new { Name = "压缩比过高", StartBit = 2, BitCount = 1 },
                        new { Name = "Ps过低", StartBit = 1, BitCount = 1 },
                        new { Name = "Pd过高", StartBit = 0, BitCount = 1 }
                    }
                }),
                Unit = "",
                IsReadOnly = true,
                DisplayOrder = 3,
                IsActive = true
            }
        };
    }

    /// <summary>
    /// 获取格式2参数定义种子数据
    /// </summary>
    /// <param name="templateId">参数模板ID</param>
    /// <returns>参数定义数组</returns>
    public static ParameterDefinition[] GetFormat2Parameters(int templateId)
    {
        return new[]
        {
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0xA1,
                ParameterIndex = 0xFF, // 特殊索引表示系统标志
                Name = "系统标志位",
                Description = "系统配置和模式标志位",
                Category = "System",
                DataType = "u8",
                ValueFormat = ParameterValueFormat.BitField,
                FormatConfiguration = JsonSerializer.Serialize(new
                {
                    BitDefinitions = new object[]
                    {
                        new { Name = "支持内机过冷/热度修正标志", StartBit = 7, BitCount = 1 },
                        new { Name = "是否允许自动地址设定", StartBit = 6, BitCount = 1 },
                        new { Name = "内机地址竞争方式", StartBit = 5, BitCount = 1 },
                        new { Name = "禁止室内机ThermoOn转换标志", StartBit = 4, BitCount = 1 },
                        new { Name = "26度节能锁定", StartBit = 3, BitCount = 1 },
                        new { Name = "系统模式优先级", StartBit = 0, BitCount = 3,
                            EnumValues = new[] { "通常", "单冷", "单热", "冷优先", "热优先", "少数服从多数" } }
                    }
                }),
                Unit = "",
                IsReadOnly = true,
                DisplayOrder = 1,
                IsActive = true
            },
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0xA1,
                ParameterIndex = 0xFE, // 特殊索引表示室外机状态
                Name = "室外机状态",
                Description = "室外机工作状态和运行模式",
                Category = "Status",
                DataType = "u8",
                ValueFormat = ParameterValueFormat.BitField,
                FormatConfiguration = JsonSerializer.Serialize(new
                {
                    BitDefinitions = new object[]
                    {
                        new { Name = "室外系统工作模式", StartBit = 6, BitCount = 2,
                            EnumValues = new[] { "停止", "制冷", "制热", "制热水" } },
                        new { Name = "压缩机运转状态", StartBit = 5, BitCount = 1 },
                        new { Name = "回油状态", StartBit = 4, BitCount = 1 },
                        new { Name = "除霜状态", StartBit = 3, BitCount = 1 },
                        new { Name = "除霜准备状态", StartBit = 2, BitCount = 1 },
                        new { Name = "四通阀状态", StartBit = 1, BitCount = 1 },
                        new { Name = "制热模式回油方式", StartBit = 0, BitCount = 1 }
                    }
                }),
                Unit = "",
                IsReadOnly = true,
                DisplayOrder = 2,
                IsActive = true
            },
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0xA1,
                ParameterIndex = 0xFD,
                Name = "高压饱和温度",
                Description = "外机系统高压饱和温度",
                Category = "Temperature",
                DataType = "u16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = JsonSerializer.Serialize(new 
                { 
                    DataType = "u16", 
                    Multiplier = 0.1, 
                    DecimalPlaces = 1 
                }),
                Unit = "°C",
                IsReadOnly = true,
                DisplayOrder = 3,
                IsActive = true
            },
            new ParameterDefinition
            {
                ParameterTemplateId = templateId,
                CommandCode = 0xA1,
                ParameterIndex = 0xFC,
                Name = "低压饱和温度",
                Description = "外机系统低压饱和温度",
                Category = "Temperature",
                DataType = "u16",
                ValueFormat = ParameterValueFormat.SingleValue,
                FormatConfiguration = JsonSerializer.Serialize(new 
                { 
                    DataType = "u16", 
                    Multiplier = 0.1, 
                    DecimalPlaces = 1 
                }),
                Unit = "°C",
                IsReadOnly = true,
                DisplayOrder = 4,
                IsActive = true
            }
        };
    }
}
