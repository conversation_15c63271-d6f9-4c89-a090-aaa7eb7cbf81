using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using AirMonitor.Infrastructure.Configuration;
using System.Diagnostics;

namespace AirMonitor.Api.Controllers;

/// <summary>
/// 系统信息控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Tags("System")]
public class SystemController : ControllerBase
{
    private readonly ILogger<SystemController> _logger;
    private readonly AppSettings _appSettings;

    public SystemController(
        ILogger<SystemController> logger,
        IOptions<AppSettings> appSettings)
    {
        _logger = logger;
        _appSettings = appSettings.Value;
    }

    /// <summary>
    /// 获取系统信息
    /// </summary>
    /// <returns>系统信息</returns>
    [HttpGet("info")]
    public IActionResult GetSystemInfo()
    {
        _logger.LogInformation("获取系统信息请求");

        var systemInfo = new
        {
            ApplicationName = _appSettings.Name,
            Version = _appSettings.Version,
            Environment = _appSettings.Environment,
            Timestamp = DateTime.UtcNow,
            Status = "运行中",
            MachineName = Environment.MachineName,
            ProcessorCount = Environment.ProcessorCount,
            WorkingSet = Environment.WorkingSet,
            OSVersion = Environment.OSVersion.ToString(),
            RuntimeVersion = Environment.Version.ToString()
        };

        return Ok(systemInfo);
    }

    /// <summary>
    /// 获取应用配置信息
    /// </summary>
    /// <returns>配置信息</returns>
    [HttpGet("config")]
    public IActionResult GetConfiguration()
    {
        _logger.LogInformation("获取配置信息请求");

        var configInfo = new
        {
            DataCollectionInterval = _appSettings.DataCollectionInterval,
            BatchProcessingSize = _appSettings.BatchProcessingSize,
            MaxConcurrentDevices = _appSettings.MaxConcurrentDevices,
            DataRetentionDays = _appSettings.DataRetentionDays,
            EnablePerformanceMonitoring = _appSettings.EnablePerformanceMonitoring,
            EnableHealthChecks = _appSettings.EnableHealthChecks,
            HealthCheckInterval = _appSettings.HealthCheckInterval
        };

        return Ok(configInfo);
    }

    /// <summary>
    /// 健康检查端点
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet("health")]
    public IActionResult HealthCheck()
    {
        _logger.LogDebug("健康检查请求");

        var healthStatus = new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime,
            Checks = new
            {
                Application = "Healthy",
                Memory = GC.GetTotalMemory(false) < 500 * 1024 * 1024 ? "Healthy" : "Warning", // 500MB 阈值
                ThreadCount = Process.GetCurrentProcess().Threads.Count < 100 ? "Healthy" : "Warning"
            }
        };

        return Ok(healthStatus);
    }
}

/// <summary>
/// 系统信息响应模型
/// </summary>
public class SystemInfoResponse
{
    public string ApplicationName { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Environment { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string Status { get; set; } = string.Empty;
}
