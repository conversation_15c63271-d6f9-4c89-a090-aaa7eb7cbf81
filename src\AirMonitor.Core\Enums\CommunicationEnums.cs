namespace AirMonitor.Core.Enums;

/// <summary>
/// 通信类型枚举
/// </summary>
public enum CommunicationType
{
    /// <summary>
    /// 点播读取
    /// </summary>
    PointRead = 1,
    
    /// <summary>
    /// 控制命令
    /// </summary>
    ControlCommand = 2
}

/// <summary>
/// 协议格式枚举
/// </summary>
public enum ProtocolFormat
{
    /// <summary>
    /// 参数索引格式（变长）
    /// </summary>
    ParameterIndex = 1,
    
    /// <summary>
    /// 自定义数据格式（定长）
    /// </summary>
    CustomData = 2
}

/// <summary>
/// 数据质量枚举
/// </summary>
public enum DataQuality
{
    /// <summary>
    /// 数据良好
    /// </summary>
    Good = 1,
    
    /// <summary>
    /// 数据不确定
    /// </summary>
    Uncertain = 2,
    
    /// <summary>
    /// 数据错误
    /// </summary>
    Bad = 3
}
