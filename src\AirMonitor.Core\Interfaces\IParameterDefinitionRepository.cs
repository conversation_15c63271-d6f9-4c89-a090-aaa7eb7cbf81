using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 参数定义仓储接口
/// </summary>
public interface IParameterDefinitionRepository
{
    /// <summary>
    /// 根据命令码和参数索引获取参数定义
    /// </summary>
    /// <param name="commandCode">命令码</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>参数定义</returns>
    Task<ParameterDefinition?> GetByCommandAndIndexAsync(byte commandCode, byte parameterIndex);
    
    /// <summary>
    /// 根据ID获取参数定义
    /// </summary>
    /// <param name="id">参数定义ID</param>
    /// <returns>参数定义</returns>
    Task<ParameterDefinition?> GetDefinitionByIdAsync(int id);
    
    /// <summary>
    /// 根据设备类型获取参数定义列表
    /// </summary>
    /// <param name="deviceType">设备类型</param>
    /// <returns>参数定义集合</returns>
    Task<IEnumerable<ParameterDefinition>> GetByDeviceTypeAsync(DeviceType deviceType);
    
    /// <summary>
    /// 根据设备和参数索引获取参数定义
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>参数定义</returns>
    Task<ParameterDefinition?> GetByDeviceAndIndexAsync(int deviceId, byte parameterIndex);
    
    /// <summary>
    /// 获取所有激活的参数定义
    /// </summary>
    /// <returns>参数定义集合</returns>
    Task<IEnumerable<ParameterDefinition>> GetActiveDefinitionsAsync();
}
