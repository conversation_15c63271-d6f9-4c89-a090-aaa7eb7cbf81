namespace AirMonitor.Core.Enums;

/// <summary>
/// 设备类型枚举
/// </summary>
public enum DeviceType
{
    /// <summary>
    /// 室外机
    /// </summary>
    OutdoorUnit = 1,
    
    /// <summary>
    /// 室内机
    /// </summary>
    IndoorUnit = 2,
    
    /// <summary>
    /// 集中控制器
    /// </summary>
    Controller = 3
}

/// <summary>
/// 设备状态枚举
/// </summary>
public enum DeviceStatus
{
    /// <summary>
    /// 未知状态
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// 正常运行
    /// </summary>
    Normal = 1,
    
    /// <summary>
    /// 警告状态
    /// </summary>
    Warning = 2,
    
    /// <summary>
    /// 错误状态
    /// </summary>
    Error = 3,
    
    /// <summary>
    /// 维护状态
    /// </summary>
    Maintenance = 4
}
