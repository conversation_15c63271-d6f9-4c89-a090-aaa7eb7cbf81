namespace AirMonitor.Infrastructure.Configuration;

/// <summary>
/// 数据库配置设置
/// </summary>
public class DatabaseSettings
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "Database";

    /// <summary>
    /// 主数据库连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Redis 缓存连接字符串
    /// </summary>
    public string RedisConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 命令超时时间（秒）
    /// </summary>
    public int CommandTimeout { get; set; } = 30;

    /// <summary>
    /// 连接池最大大小
    /// </summary>
    public int MaxPoolSize { get; set; } = 100;

    /// <summary>
    /// 连接池最小大小
    /// </summary>
    public int MinPoolSize { get; set; } = 5;

    /// <summary>
    /// 是否启用敏感数据日志记录
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// 是否启用详细错误信息
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = false;

    /// <summary>
    /// 是否启用自动迁移
    /// </summary>
    public bool EnableAutoMigration { get; set; } = false;

    /// <summary>
    /// 批量操作大小
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// 查询跟踪行为
    /// </summary>
    public string QueryTrackingBehavior { get; set; } = "TrackAll";

    /// <summary>
    /// 是否启用查询拆分行为
    /// </summary>
    public bool EnableSplitQueries { get; set; } = true;

    /// <summary>
    /// 数据库重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 数据库重试延迟（毫秒）
    /// </summary>
    public int RetryDelay { get; set; } = 1000;
}
