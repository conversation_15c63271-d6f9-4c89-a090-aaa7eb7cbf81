using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Enums;
using AirMonitor.Core.Interfaces;

namespace AirMonitor.Infrastructure.Data.Repositories;

/// <summary>
/// 设备仓储实现
/// </summary>
public class DeviceRepository : IDeviceRepository
{
    private readonly AirMonitorDbContext _context;

    public DeviceRepository(AirMonitorDbContext context)
    {
        _context = context;
    }

    public async Task<Device?> GetByAddressAsync(byte deviceAddress)
    {
        return await _context.Devices
            .FirstOrDefaultAsync(d => d.DeviceAddress == deviceAddress);
    }

    public async Task<IEnumerable<Device>> GetOnlineDevicesAsync()
    {
        return await _context.Devices
            .Where(d => d.IsOnline)
            .ToListAsync();
    }

    public async Task<IEnumerable<Device>> GetByTypeAsync(DeviceType deviceType)
    {
        return await _context.Devices
            .Where(d => d.DeviceType == deviceType)
            .ToListAsync();
    }

    public async Task UpdateOnlineStatusAsync(byte deviceAddress, bool isOnline)
    {
        var device = await GetByAddressAsync(deviceAddress);
        if (device != null)
        {
            device.IsOnline = isOnline;
            device.LastCommunication = DateTimeOffset.UtcNow;
            device.UpdatedAt = DateTimeOffset.UtcNow;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Device> CreateAsync(Device device)
    {
        device.CreatedAt = DateTimeOffset.UtcNow;
        device.UpdatedAt = DateTimeOffset.UtcNow;
        
        _context.Devices.Add(device);
        await _context.SaveChangesAsync();
        return device;
    }

    public async Task<Device> UpdateAsync(Device device)
    {
        device.UpdatedAt = DateTimeOffset.UtcNow;
        
        _context.Devices.Update(device);
        await _context.SaveChangesAsync();
        return device;
    }

    public async Task<Device?> GetByIdAsync(int id)
    {
        return await _context.Devices
            .FirstOrDefaultAsync(d => d.Id == id);
    }

    public async Task<IEnumerable<Device>> GetAllAsync()
    {
        return await _context.Devices.ToListAsync();
    }
}
