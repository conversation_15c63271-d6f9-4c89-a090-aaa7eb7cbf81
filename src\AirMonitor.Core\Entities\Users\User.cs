using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Entities.Users;

/// <summary>
/// 用户实体
/// </summary>
public class User
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 密码哈希
    /// </summary>
    public string PasswordHash { get; set; } = string.Empty;
    
    /// <summary>
    /// 用户角色
    /// </summary>
    public UserRole Role { get; set; }
    
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }
    
    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTimeOffset? LastLoginAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 设备权限集合
    /// </summary>
    public virtual ICollection<UserDevicePermission> DevicePermissions { get; set; } = new List<UserDevicePermission>();
}
