using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using AirMonitor.Infrastructure.Configuration;

namespace AirMonitor.Infrastructure.DependencyInjection;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 AirMonitor 基础架构服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddAirMonitorInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // 配置选项
        services.ConfigureOptions(configuration);

        // 基础设施服务
        services.AddInfrastructureServices();

        // 健康检查
        services.AddHealthChecks(configuration);

        // CORS 配置
        services.AddCorsConfiguration(configuration);

        return services;
    }

    /// <summary>
    /// 配置选项
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureOptions(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // 应用程序配置
        services.Configure<AppSettings>(options =>
            configuration.GetSection(AppSettings.SectionName).Bind(options));

        // API 配置
        services.Configure<ApiSettings>(options =>
            configuration.GetSection(ApiSettings.SectionName).Bind(options));

        // 数据库配置
        services.Configure<DatabaseSettings>(options =>
            configuration.GetSection(DatabaseSettings.SectionName).Bind(options));

        // 串口配置
        services.Configure<SerialPortSettings>(options =>
            configuration.GetSection(SerialPortSettings.SectionName).Bind(options));

        // 验证配置
        services.AddSingleton<IValidateOptions<AppSettings>, AppSettingsValidator>();
        services.AddSingleton<IValidateOptions<DatabaseSettings>, DatabaseSettingsValidator>();
        services.AddSingleton<IValidateOptions<SerialPortSettings>, SerialPortSettingsValidator>();

        return services;
    }

    /// <summary>
    /// 添加基础设施服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructureServices(
        this IServiceCollection services)
    {
        // 这里将添加具体的基础设施服务
        // 例如：数据访问、缓存、消息队列等

        return services;
    }

    /// <summary>
    /// 添加健康检查
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddHealthChecks(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var healthChecksBuilder = services.AddHealthChecks();

        // 基础健康检查已添加
        // 数据库和 Redis 健康检查将在安装相应的 NuGet 包后添加
        // 例如：AspNetCore.HealthChecks.SqlServer 和 AspNetCore.HealthChecks.Redis

        return services;
    }

    /// <summary>
    /// 添加 CORS 配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCorsConfiguration(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var apiSettings = new ApiSettings();
        configuration.GetSection(ApiSettings.SectionName).Bind(apiSettings);
        var corsSettings = apiSettings.Cors ?? new CorsSettings();

        if (corsSettings.Enabled)
        {
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                {
                    // 检查是否使用通配符来源
                    var hasWildcardOrigin = corsSettings.AllowedOrigins.Contains("*");

                    if (hasWildcardOrigin)
                    {
                        // 如果使用通配符来源，则不能同时允许凭据
                        builder.AllowAnyOrigin();

                        // 不设置 AllowCredentials，因为与 AllowAnyOrigin 冲突
                    }
                    else
                    {
                        // 如果指定了具体来源，可以允许凭据
                        builder.WithOrigins(corsSettings.AllowedOrigins);

                        if (corsSettings.AllowCredentials)
                        {
                            builder.AllowCredentials();
                        }
                    }

                    if (corsSettings.AllowedMethods.Contains("*"))
                    {
                        builder.AllowAnyMethod();
                    }
                    else
                    {
                        builder.WithMethods(corsSettings.AllowedMethods);
                    }

                    if (corsSettings.AllowedHeaders.Contains("*"))
                    {
                        builder.AllowAnyHeader();
                    }
                    else
                    {
                        builder.WithHeaders(corsSettings.AllowedHeaders);
                    }

                    builder.SetPreflightMaxAge(TimeSpan.FromSeconds(corsSettings.PreflightMaxAge));
                });
            });
        }

        return services;
    }
}
