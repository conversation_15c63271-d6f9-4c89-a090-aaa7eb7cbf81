using Microsoft.Extensions.Options;

namespace AirMonitor.Infrastructure.Configuration;

/// <summary>
/// 应用程序配置验证器
/// </summary>
public class AppSettingsValidator : IValidateOptions<AppSettings>
{
    public ValidateOptionsResult Validate(string? name, AppSettings options)
    {
        var failures = new List<string>();

        if (string.IsNullOrWhiteSpace(options.Name))
        {
            failures.Add("应用程序名称不能为空");
        }

        if (string.IsNullOrWhiteSpace(options.Version))
        {
            failures.Add("应用程序版本不能为空");
        }

        if (options.DataCollectionInterval <= 0)
        {
            failures.Add("数据采集间隔必须大于0");
        }

        if (options.BatchProcessingSize <= 0)
        {
            failures.Add("批量处理大小必须大于0");
        }

        if (options.MaxConcurrentDevices <= 0)
        {
            failures.Add("最大并发设备数必须大于0");
        }

        if (options.DataRetentionDays <= 0)
        {
            failures.Add("数据保留天数必须大于0");
        }

        if (options.HealthCheckInterval <= 0)
        {
            failures.Add("健康检查间隔必须大于0");
        }

        return failures.Count > 0
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}

/// <summary>
/// 数据库配置验证器
/// </summary>
public class DatabaseSettingsValidator : IValidateOptions<DatabaseSettings>
{
    public ValidateOptionsResult Validate(string? name, DatabaseSettings options)
    {
        var failures = new List<string>();

        if (string.IsNullOrWhiteSpace(options.ConnectionString))
        {
            failures.Add("数据库连接字符串不能为空");
        }

        if (options.CommandTimeout <= 0)
        {
            failures.Add("命令超时时间必须大于0");
        }

        if (options.MaxPoolSize <= 0)
        {
            failures.Add("连接池最大大小必须大于0");
        }

        if (options.MinPoolSize < 0)
        {
            failures.Add("连接池最小大小不能小于0");
        }

        if (options.MinPoolSize >= options.MaxPoolSize)
        {
            failures.Add("连接池最小大小必须小于最大大小");
        }

        if (options.BatchSize <= 0)
        {
            failures.Add("批量操作大小必须大于0");
        }

        if (options.RetryCount < 0)
        {
            failures.Add("重试次数不能小于0");
        }

        if (options.RetryDelay <= 0)
        {
            failures.Add("重试延迟必须大于0");
        }

        return failures.Count > 0
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}

/// <summary>
/// 串口配置验证器
/// </summary>
public class SerialPortSettingsValidator : IValidateOptions<SerialPortSettings>
{
    public ValidateOptionsResult Validate(string? name, SerialPortSettings options)
    {
        var failures = new List<string>();

        if (string.IsNullOrWhiteSpace(options.DefaultPortName))
        {
            failures.Add("默认串口名称不能为空");
        }

        if (options.BaudRate <= 0)
        {
            failures.Add("波特率必须大于0");
        }

        if (options.DataBits < 5 || options.DataBits > 8)
        {
            failures.Add("数据位必须在5-8之间");
        }

        if (options.ReadTimeout <= 0)
        {
            failures.Add("读取超时时间必须大于0");
        }

        if (options.WriteTimeout <= 0)
        {
            failures.Add("写入超时时间必须大于0");
        }

        if (options.ReceiveBufferSize <= 0)
        {
            failures.Add("接收缓冲区大小必须大于0");
        }

        if (options.SendBufferSize <= 0)
        {
            failures.Add("发送缓冲区大小必须大于0");
        }

        if (options.RetryCount < 0)
        {
            failures.Add("重试次数不能小于0");
        }

        if (options.RetryInterval <= 0)
        {
            failures.Add("重试间隔必须大于0");
        }

        if (options.HeartbeatInterval <= 0)
        {
            failures.Add("心跳检测间隔必须大于0");
        }

        return failures.Count > 0
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}
