using AirMonitor.Core.Enums;
using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Entities.Parameters;

namespace AirMonitor.Core.Entities.Monitoring;

/// <summary>
/// 监控数据实体
/// </summary>
public class MonitoringData
{
    /// <summary>
    /// 监控数据ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public int DeviceId { get; set; }
    
    /// <summary>
    /// 设备参数ID
    /// </summary>
    public int DeviceParameterId { get; set; }
    
    /// <summary>
    /// 参数索引
    /// </summary>
    public byte ParameterIndex { get; set; }
    
    /// <summary>
    /// 原始值
    /// </summary>
    public ushort RawValue { get; set; }
    
    /// <summary>
    /// 解析后的值(JSON)
    /// </summary>
    public string? ParsedValue { get; set; }
    
    /// <summary>
    /// 数值型值
    /// </summary>
    public decimal? NumericValue { get; set; }
    
    /// <summary>
    /// 字符串值
    /// </summary>
    public string? StringValue { get; set; }
    
    /// <summary>
    /// 布尔值
    /// </summary>
    public bool? BooleanValue { get; set; }
    
    /// <summary>
    /// 数据质量
    /// </summary>
    public DataQuality Quality { get; set; }
    
    /// <summary>
    /// 数据时间戳
    /// </summary>
    public DateTimeOffset Timestamp { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 设备
    /// </summary>
    public virtual Device Device { get; set; } = null!;
    
    /// <summary>
    /// 设备参数
    /// </summary>
    public virtual DeviceParameter DeviceParameter { get; set; } = null!;
}
