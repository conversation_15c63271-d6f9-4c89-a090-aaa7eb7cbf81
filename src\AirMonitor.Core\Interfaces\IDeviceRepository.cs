using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 设备仓储接口
/// </summary>
public interface IDeviceRepository
{
    /// <summary>
    /// 根据设备地址获取设备
    /// </summary>
    /// <param name="deviceAddress">设备地址</param>
    /// <returns>设备实体</returns>
    Task<Device?> GetByAddressAsync(byte deviceAddress);
    
    /// <summary>
    /// 获取在线设备列表
    /// </summary>
    /// <returns>在线设备集合</returns>
    Task<IEnumerable<Device>> GetOnlineDevicesAsync();
    
    /// <summary>
    /// 根据设备类型获取设备列表
    /// </summary>
    /// <param name="deviceType">设备类型</param>
    /// <returns>设备集合</returns>
    Task<IEnumerable<Device>> GetByTypeAsync(DeviceType deviceType);
    
    /// <summary>
    /// 更新设备在线状态
    /// </summary>
    /// <param name="deviceAddress">设备地址</param>
    /// <param name="isOnline">是否在线</param>
    /// <returns>任务</returns>
    Task UpdateOnlineStatusAsync(byte deviceAddress, bool isOnline);
    
    /// <summary>
    /// 创建设备
    /// </summary>
    /// <param name="device">设备实体</param>
    /// <returns>创建的设备</returns>
    Task<Device> CreateAsync(Device device);
    
    /// <summary>
    /// 更新设备
    /// </summary>
    /// <param name="device">设备实体</param>
    /// <returns>更新的设备</returns>
    Task<Device> UpdateAsync(Device device);
    
    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>设备实体</returns>
    Task<Device?> GetByIdAsync(int id);
    
    /// <summary>
    /// 获取所有设备
    /// </summary>
    /// <returns>设备集合</returns>
    Task<IEnumerable<Device>> GetAllAsync();
}
