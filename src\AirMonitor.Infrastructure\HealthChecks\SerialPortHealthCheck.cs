using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Options;
using System.IO.Ports;
using AirMonitor.Infrastructure.Configuration;

namespace AirMonitor.Infrastructure.HealthChecks;

/// <summary>
/// 串口健康检查
/// </summary>
public class SerialPortHealthCheck : IHealthCheck
{
    private readonly SerialPortSettings _settings;

    public SerialPortHealthCheck(IOptions<SerialPortSettings> settings)
    {
        _settings = settings.Value;
    }

    /// <summary>
    /// 检查健康状态
    /// </summary>
    /// <param name="context">健康检查上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康检查结果</returns>
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 获取可用串口列表
            var availablePorts = SerialPort.GetPortNames();
            var data = new Dictionary<string, object>
            {
                ["availablePorts"] = availablePorts,
                ["defaultPort"] = _settings.DefaultPortName,
                ["configuredBaudRate"] = _settings.BaudRate
            };

            if (availablePorts.Length == 0)
            {
                return HealthCheckResult.Degraded(
                    "未发现任何可用的串口",
                    data: data);
            }

            // 检查默认串口是否可用
            var defaultPortAvailable = availablePorts.Contains(_settings.DefaultPortName);
            if (!defaultPortAvailable)
            {
                return HealthCheckResult.Degraded(
                    $"默认串口 {_settings.DefaultPortName} 不可用，但发现其他可用串口",
                    data: data);
            }

            // 尝试打开默认串口进行连接测试
            var connectionTestResult = await TestSerialPortConnectionAsync(cancellationToken);
            data["connectionTest"] = connectionTestResult.Success;
            data["connectionMessage"] = connectionTestResult.Message;

            if (!connectionTestResult.Success)
            {
                return HealthCheckResult.Degraded(
                    $"串口 {_settings.DefaultPortName} 连接测试失败: {connectionTestResult.Message}",
                    data: data);
            }

            return HealthCheckResult.Healthy(
                $"发现 {availablePorts.Length} 个可用串口，默认串口 {_settings.DefaultPortName} 连接正常",
                data: data);
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy(
                "串口健康检查失败",
                ex,
                new Dictionary<string, object>
                {
                    ["error"] = ex.Message,
                    ["defaultPort"] = _settings.DefaultPortName
                });
        }
    }

    /// <summary>
    /// 测试串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接测试结果</returns>
    private async Task<(bool Success, string Message)> TestSerialPortConnectionAsync(
        CancellationToken cancellationToken)
    {
        SerialPort? serialPort = null;
        try
        {
            serialPort = new SerialPort(_settings.DefaultPortName)
            {
                BaudRate = _settings.BaudRate,
                DataBits = _settings.DataBits,
                StopBits = _settings.StopBits,
                Parity = _settings.Parity,
                ReadTimeout = Math.Min(_settings.ReadTimeout, 2000), // 健康检查使用较短的超时时间
                WriteTimeout = Math.Min(_settings.WriteTimeout, 2000)
            };

            // 尝试打开串口
            await Task.Run(() => serialPort.Open(), cancellationToken);

            // 检查串口状态
            if (serialPort.IsOpen)
            {
                // 可以在这里添加更多的连接测试逻辑
                // 例如发送心跳包或读取设备状态

                return (true, "串口连接测试成功");
            }

            return (false, "串口打开失败");
        }
        catch (UnauthorizedAccessException)
        {
            return (false, "串口被其他程序占用");
        }
        catch (ArgumentException ex)
        {
            return (false, $"串口参数无效: {ex.Message}");
        }
        catch (InvalidOperationException ex)
        {
            return (false, $"串口操作无效: {ex.Message}");
        }
        catch (IOException ex)
        {
            return (false, $"串口IO错误: {ex.Message}");
        }
        catch (TimeoutException)
        {
            return (false, "串口连接超时");
        }
        catch (Exception ex)
        {
            return (false, $"串口连接测试异常: {ex.Message}");
        }
        finally
        {
            try
            {
                if (serialPort?.IsOpen == true)
                {
                    serialPort.Close();
                }
                serialPort?.Dispose();
            }
            catch
            {
                // 忽略关闭时的异常
            }
        }
    }
}
