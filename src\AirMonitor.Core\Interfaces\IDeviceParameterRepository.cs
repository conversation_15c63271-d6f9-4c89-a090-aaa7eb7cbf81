using AirMonitor.Core.Entities.Parameters;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 设备参数仓储接口
/// </summary>
public interface IDeviceParameterRepository
{
    /// <summary>
    /// 获取或创建设备参数
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="parameterDefinitionId">参数定义ID</param>
    /// <returns>设备参数</returns>
    Task<DeviceParameter> GetOrCreateAsync(int deviceId, int parameterDefinitionId);
    
    /// <summary>
    /// 根据设备和参数定义获取设备参数
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="parameterDefinitionId">参数定义ID</param>
    /// <returns>设备参数</returns>
    Task<DeviceParameter?> GetByDeviceAndParameterAsync(int deviceId, int parameterDefinitionId);
    
    /// <summary>
    /// 创建设备参数
    /// </summary>
    /// <param name="deviceParameter">设备参数</param>
    /// <returns>创建的设备参数</returns>
    Task<DeviceParameter> CreateAsync(DeviceParameter deviceParameter);
    
    /// <summary>
    /// 获取设备的所有参数
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备参数集合</returns>
    Task<IEnumerable<DeviceParameter>> GetByDeviceAsync(int deviceId);
}
