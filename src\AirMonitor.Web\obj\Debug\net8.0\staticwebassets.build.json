{"Version": 1, "Hash": "O3CSpFA9nBpxH4c/2J5Y/0YvQe8Bscy1IQv0NhFeqWA=", "Source": "AirMonitor.Web", "BasePath": "_content/AirMonitor.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AirMonitor.Web\\wwwroot", "Source": "AirMonitor.Web", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\", "BasePath": "_content/AirMonitor.Web", "Pattern": "**"}], "Assets": [{"Identity": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AirMonitor.Web.styles.css", "SourceId": "AirMonitor.Web", "SourceType": "Computed", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/AirMonitor.Web", "RelativePath": "AirMonitor.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "eh6mbgdbyx", "Integrity": "rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AirMonitor.Web.styles.css", "FileLength": 5911, "LastWriteTime": "2025-06-07T15:01:11+00:00"}, {"Identity": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AirMonitor.Web.bundle.scp.css", "SourceId": "AirMonitor.Web", "SourceType": "Computed", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/AirMonitor.Web", "RelativePath": "AirMonitor.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "eh6mbgdbyx", "Integrity": "rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AirMonitor.Web.bundle.scp.css", "FileLength": 5911, "LastWriteTime": "2025-06-07T15:01:11+00:00"}, {"Identity": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\app.css", "SourceId": "AirMonitor.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\", "BasePath": "_content/AirMonitor.Web", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "da95v2qkru", "Integrity": "u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2591, "LastWriteTime": "2025-06-07T14:38:23+00:00"}, {"Identity": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "AirMonitor.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\", "BasePath": "_content/AirMonitor.Web", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-07T14:38:23+00:00"}, {"Identity": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "AirMonitor.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\", "BasePath": "_content/AirMonitor.Web", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-07T14:38:23+00:00"}, {"Identity": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\favicon.png", "SourceId": "AirMonitor.Web", "SourceType": "Discovered", "ContentRoot": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\", "BasePath": "_content/AirMonitor.Web", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-07T14:38:23+00:00"}], "Endpoints": [{"Route": "AirMonitor.Web.bundle.scp.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AirMonitor.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 15:01:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs="}]}, {"Route": "AirMonitor.Web.eh6mbgdbyx.bundle.scp.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AirMonitor.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 15:01:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eh6mbgdbyx"}, {"Name": "label", "Value": "AirMonitor.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs="}]}, {"Route": "AirMonitor.Web.eh6mbgdbyx.styles.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AirMonitor.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 15:01:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eh6mbgdbyx"}, {"Name": "label", "Value": "AirMonitor.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs="}]}, {"Route": "AirMonitor.Web.styles.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AirMonitor.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 15:01:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rnqVjMFo2o6JwlWDR2X9lspFFtCHhgqn0zpwkalCKvs="}]}, {"Route": "app.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Web\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 Jun 2025 14:38:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}]}