2025-06-08 11:50:17.798 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:50:18.098 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:50:18.102 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:50:18.171 +08:00 [INF] No migrations were found in assembly 'AirMonitor.Infrastructure'. A migration needs to be added before the database can be updated.
2025-06-08 11:51:24.340 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:51:24.589 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:51:24.593 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:51:24.653 +08:00 [INF] No migrations were found in assembly 'AirMonitor.Infrastructure'. A migration needs to be added before the database can be updated.
2025-06-08 11:52:40.738 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:52:40.758 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:52:50.134 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:52:50.374 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:52:50.377 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:52:50.513 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
PRAGMA journal_mode = 'wal';
2025-06-08 11:52:50.518 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:52:50.522 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:52:50.535 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsLock" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK___EFMigrationsLock" PRIMARY KEY,
    "Timestamp" TEXT NOT NULL
);
2025-06-08 11:52:50.539 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:52:50.5367246+00:00');
SELECT changes();
2025-06-08 11:52:50.564 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:52:50.574 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:52:50.576 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:52:50.581 +08:00 [INF] Applying migration '20250608035241_InitialCreate'.
2025-06-08 11:52:50.594 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Devices" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Devices" PRIMARY KEY AUTOINCREMENT,

    -- RS485设备地址
    "DeviceAddress" INTEGER NOT NULL,

    -- 设备类型
    "DeviceType" INTEGER NOT NULL,

    -- 设备序列号
    "SerialNumber" TEXT NULL,

    -- 设备型号
    "ModelNumber" TEXT NOT NULL,

    -- 固件版本
    "FirmwareVersion" TEXT NOT NULL,

    -- 设备状态
    "Status" INTEGER NOT NULL,

    "LastCommunication" TEXT NOT NULL,

    "IsOnline" INTEGER NOT NULL,

    -- 设备位置
    "Location" TEXT NULL,

    -- 设备描述
    "Description" TEXT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:52:50.597 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterTemplates" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterTemplates" PRIMARY KEY AUTOINCREMENT,

    -- 模板名称
    "Name" TEXT NOT NULL,

    -- 模板描述
    "Description" TEXT NOT NULL,

    -- 适用的设备类型
    "ApplicableDeviceType" INTEGER NOT NULL,

    -- 设备型号匹配模式
    "ModelPattern" TEXT NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:52:50.598 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Users" PRIMARY KEY AUTOINCREMENT,

    -- 用户名
    "Username" TEXT NOT NULL,

    -- 邮箱
    "Email" TEXT NOT NULL,

    -- 密码哈希
    "PasswordHash" TEXT NOT NULL,

    -- 用户角色
    "Role" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL,

    "LastLoginAt" TEXT NULL
);
2025-06-08 11:52:50.599 +08:00 [ERR] Failed executing DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_CommunicationLogs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_CommunicationLogs" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    -- 通信类型
    "CommunicationType" INTEGER NOT NULL,

    -- 协议格式
    "ProtocolFormat" INTEGER NOT NULL,

    -- 源地址
    "SourceAddress" INTEGER NOT NULL,

    -- 目标地址
    "TargetAddress" INTEGER NOT NULL,

    -- 命令码
    "CommandCode" INTEGER NOT NULL,

    -- 原始数据
    "RawData" BLOB NOT NULL,

    -- 解析后的数据JSON
    "ParsedData" nvarchar(max) NULL,

    "IsSuccessful" INTEGER NOT NULL,

    -- 错误消息
    "ErrorMessage" TEXT NULL,

    "Timestamp" TEXT NOT NULL,
    CONSTRAINT "FK_AM_CommunicationLogs_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE
);
2025-06-08 11:54:54.057 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-08 11:54:54.300 +08:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserDevicePermission'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-06-08 11:54:54.304 +08:00 [WRN] Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-08 11:54:54.414 +08:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-08 11:54:54.425 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsLock' AND "type" = 'table';
2025-06-08 11:54:54.431 +08:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT OR IGNORE INTO "__EFMigrationsLock"("Id", "Timestamp") VALUES(1, '2025-06-08 03:54:54.4285336+00:00');
SELECT changes();
2025-06-08 11:54:54.455 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" TEXT NOT NULL CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY,
    "ProductVersion" TEXT NOT NULL
);
2025-06-08 11:54:54.461 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*) FROM "sqlite_master" WHERE "name" = '__EFMigrationsHistory' AND "type" = 'table';
2025-06-08 11:54:54.464 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-08 11:54:54.469 +08:00 [INF] Applying migration '20250608035241_InitialCreate'.
2025-06-08 11:54:54.484 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Devices" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Devices" PRIMARY KEY AUTOINCREMENT,

    -- RS485设备地址
    "DeviceAddress" INTEGER NOT NULL,

    -- 设备类型
    "DeviceType" INTEGER NOT NULL,

    -- 设备序列号
    "SerialNumber" TEXT NULL,

    -- 设备型号
    "ModelNumber" TEXT NOT NULL,

    -- 固件版本
    "FirmwareVersion" TEXT NOT NULL,

    -- 设备状态
    "Status" INTEGER NOT NULL,

    "LastCommunication" TEXT NOT NULL,

    "IsOnline" INTEGER NOT NULL,

    -- 设备位置
    "Location" TEXT NULL,

    -- 设备描述
    "Description" TEXT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:54:54.485 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_ParameterTemplates" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_ParameterTemplates" PRIMARY KEY AUTOINCREMENT,

    -- 模板名称
    "Name" TEXT NOT NULL,

    -- 模板描述
    "Description" TEXT NOT NULL,

    -- 适用的设备类型
    "ApplicableDeviceType" INTEGER NOT NULL,

    -- 设备型号匹配模式
    "ModelPattern" TEXT NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL
);
2025-06-08 11:54:54.487 +08:00 [INF] Executed DbCommand (0ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_Users" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_Users" PRIMARY KEY AUTOINCREMENT,

    -- 用户名
    "Username" TEXT NOT NULL,

    -- 邮箱
    "Email" TEXT NOT NULL,

    -- 密码哈希
    "PasswordHash" TEXT NOT NULL,

    -- 用户角色
    "Role" INTEGER NOT NULL,

    "IsActive" INTEGER NOT NULL,

    "CreatedAt" TEXT NOT NULL,

    "UpdatedAt" TEXT NOT NULL,

    "LastLoginAt" TEXT NULL
);
2025-06-08 11:54:54.489 +08:00 [ERR] Failed executing DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AM_CommunicationLogs" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_AM_CommunicationLogs" PRIMARY KEY AUTOINCREMENT,

    "DeviceId" INTEGER NOT NULL,

    -- 通信类型
    "CommunicationType" INTEGER NOT NULL,

    -- 协议格式
    "ProtocolFormat" INTEGER NOT NULL,

    -- 源地址
    "SourceAddress" INTEGER NOT NULL,

    -- 目标地址
    "TargetAddress" INTEGER NOT NULL,

    -- 命令码
    "CommandCode" INTEGER NOT NULL,

    -- 原始数据
    "RawData" BLOB NOT NULL,

    -- 解析后的数据JSON
    "ParsedData" nvarchar(max) NULL,

    "IsSuccessful" INTEGER NOT NULL,

    -- 错误消息
    "ErrorMessage" TEXT NULL,

    "Timestamp" TEXT NOT NULL,
    CONSTRAINT "FK_AM_CommunicationLogs_AM_Devices_DeviceId" FOREIGN KEY ("DeviceId") REFERENCES "AM_Devices" ("Id") ON DELETE CASCADE
);
