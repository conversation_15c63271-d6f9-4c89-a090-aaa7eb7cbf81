using AirMonitor.Core.Enums;

namespace AirMonitor.Core.Entities.Parameters;

/// <summary>
/// 参数定义实体
/// </summary>
public class ParameterDefinition
{
    /// <summary>
    /// 参数定义ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 参数模板ID
    /// </summary>
    public int ParameterTemplateId { get; set; }
    
    /// <summary>
    /// 关联的命令码
    /// </summary>
    public byte CommandCode { get; set; }
    
    /// <summary>
    /// 参数索引
    /// </summary>
    public byte ParameterIndex { get; set; }
    
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 参数类别
    /// </summary>
    public string Category { get; set; } = string.Empty;
    
    /// <summary>
    /// 数据类型(u16,s16,u32,s32)
    /// </summary>
    public string DataType { get; set; } = string.Empty;
    
    /// <summary>
    /// 参数值格式
    /// </summary>
    public ParameterValueFormat ValueFormat { get; set; }
    
    /// <summary>
    /// 格式配置(JSON)
    /// </summary>
    public string? FormatConfiguration { get; set; }
    
    /// <summary>
    /// 参数单位
    /// </summary>
    public string Unit { get; set; } = string.Empty;
    
    /// <summary>
    /// 最小值
    /// </summary>
    public decimal? MinValue { get; set; }
    
    /// <summary>
    /// 最大值
    /// </summary>
    public decimal? MaxValue { get; set; }
    
    /// <summary>
    /// 是否只读
    /// </summary>
    public bool IsReadOnly { get; set; }
    
    /// <summary>
    /// 显示顺序
    /// </summary>
    public int DisplayOrder { get; set; }
    
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 参数模板
    /// </summary>
    public virtual ParameterTemplate ParameterTemplate { get; set; } = null!;
    
    /// <summary>
    /// 设备参数集合
    /// </summary>
    public virtual ICollection<DeviceParameter> DeviceParameters { get; set; } = new List<DeviceParameter>();
}
