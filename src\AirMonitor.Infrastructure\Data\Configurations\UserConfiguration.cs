using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Users;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 用户实体配置
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.Username)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("用户名");
            
        builder.Property(x => x.Email)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("邮箱");
            
        builder.Property(x => x.PasswordHash)
            .IsRequired()
            .HasMaxLength(500)
            .HasComment("密码哈希");
            
        builder.Property(x => x.Role)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("用户角色");
        
        // 索引
        builder.HasIndex(x => x.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");
            
        builder.HasIndex(x => x.Email)
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");
            
        builder.HasIndex(x => x.Role)
            .HasDatabaseName("IX_Users_Role");
            
        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("IX_Users_IsActive");
        
        // 关系配置
        builder.HasMany(x => x.DevicePermissions)
            .WithOne(x => x.User)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
