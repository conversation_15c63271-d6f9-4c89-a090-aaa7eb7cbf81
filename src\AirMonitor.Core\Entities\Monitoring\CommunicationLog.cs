using AirMonitor.Core.Enums;
using AirMonitor.Core.Entities.Devices;

namespace AirMonitor.Core.Entities.Monitoring;

/// <summary>
/// 通信日志实体
/// </summary>
public class CommunicationLog
{
    /// <summary>
    /// 通信日志ID
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    public int DeviceId { get; set; }
    
    /// <summary>
    /// 通信类型
    /// </summary>
    public CommunicationType CommunicationType { get; set; }
    
    /// <summary>
    /// 协议格式
    /// </summary>
    public ProtocolFormat ProtocolFormat { get; set; }
    
    /// <summary>
    /// 源地址
    /// </summary>
    public byte SourceAddress { get; set; }
    
    /// <summary>
    /// 目标地址
    /// </summary>
    public byte TargetAddress { get; set; }
    
    /// <summary>
    /// 命令码
    /// </summary>
    public byte CommandCode { get; set; }
    
    /// <summary>
    /// 原始数据
    /// </summary>
    public byte[] RawData { get; set; } = Array.Empty<byte>();
    
    /// <summary>
    /// 解析后的数据(JSON)
    /// </summary>
    public string? ParsedData { get; set; }
    
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccessful { get; set; }
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTimeOffset Timestamp { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 设备
    /// </summary>
    public virtual Device Device { get; set; } = null!;
}
