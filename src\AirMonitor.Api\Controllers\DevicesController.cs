using Microsoft.AspNetCore.Mvc;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Entities.Devices;
using AirMonitor.Core.Enums;

namespace AirMonitor.Api.Controllers;

/// <summary>
/// 设备管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class DevicesController : ControllerBase
{
    private readonly IDeviceRepository _deviceRepository;
    private readonly ILogger<DevicesController> _logger;

    public DevicesController(IDeviceRepository deviceRepository, ILogger<DevicesController> logger)
    {
        _deviceRepository = deviceRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有设备
    /// </summary>
    /// <returns>设备列表</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Device>>> GetDevices()
    {
        try
        {
            var devices = await _deviceRepository.GetAllAsync();
            return Ok(devices);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备列表失败");
            return StatusCode(500, "获取设备列表失败");
        }
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>设备信息</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Device>> GetDevice(int id)
    {
        try
        {
            var device = await _deviceRepository.GetByIdAsync(id);
            if (device == null)
            {
                return NotFound($"设备 ID {id} 不存在");
            }
            return Ok(device);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 失败", id);
            return StatusCode(500, "获取设备信息失败");
        }
    }

    /// <summary>
    /// 根据设备地址获取设备
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <returns>设备信息</returns>
    [HttpGet("address/{address}")]
    public async Task<ActionResult<Device>> GetDeviceByAddress(byte address)
    {
        try
        {
            var device = await _deviceRepository.GetByAddressAsync(address);
            if (device == null)
            {
                return NotFound($"设备地址 {address:X2} 不存在");
            }
            return Ok(device);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备地址 {DeviceAddress} 失败", address);
            return StatusCode(500, "获取设备信息失败");
        }
    }

    /// <summary>
    /// 获取在线设备
    /// </summary>
    /// <returns>在线设备列表</returns>
    [HttpGet("online")]
    public async Task<ActionResult<IEnumerable<Device>>> GetOnlineDevices()
    {
        try
        {
            var devices = await _deviceRepository.GetOnlineDevicesAsync();
            return Ok(devices);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取在线设备列表失败");
            return StatusCode(500, "获取在线设备列表失败");
        }
    }

    /// <summary>
    /// 根据设备类型获取设备
    /// </summary>
    /// <param name="deviceType">设备类型</param>
    /// <returns>设备列表</returns>
    [HttpGet("type/{deviceType}")]
    public async Task<ActionResult<IEnumerable<Device>>> GetDevicesByType(DeviceType deviceType)
    {
        try
        {
            var devices = await _deviceRepository.GetByTypeAsync(deviceType);
            return Ok(devices);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备类型 {DeviceType} 列表失败", deviceType);
            return StatusCode(500, "获取设备列表失败");
        }
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <returns>创建的设备</returns>
    [HttpPost]
    public async Task<ActionResult<Device>> CreateDevice(Device device)
    {
        try
        {
            // 检查设备地址是否已存在
            var existingDevice = await _deviceRepository.GetByAddressAsync(device.DeviceAddress);
            if (existingDevice != null)
            {
                return Conflict($"设备地址 {device.DeviceAddress:X2} 已存在");
            }

            var createdDevice = await _deviceRepository.CreateAsync(device);
            return CreatedAtAction(nameof(GetDevice), new { id = createdDevice.Id }, createdDevice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建设备失败");
            return StatusCode(500, "创建设备失败");
        }
    }

    /// <summary>
    /// 更新设备在线状态
    /// </summary>
    /// <param name="address">设备地址</param>
    /// <param name="isOnline">是否在线</param>
    /// <returns>操作结果</returns>
    [HttpPut("address/{address}/status")]
    public async Task<IActionResult> UpdateDeviceOnlineStatus(byte address, [FromBody] bool isOnline)
    {
        try
        {
            await _deviceRepository.UpdateOnlineStatusAsync(address, isOnline);
            return Ok(new { message = $"设备地址 {address:X2} 状态已更新为 {(isOnline ? "在线" : "离线")}" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备 {DeviceAddress} 状态失败", address);
            return StatusCode(500, "更新设备状态失败");
        }
    }
}
