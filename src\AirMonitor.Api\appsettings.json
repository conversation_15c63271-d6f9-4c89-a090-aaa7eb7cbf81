{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=AirMonitor;Trusted_Connection=true;TrustServerCertificate=true;", "Redis": "localhost:6379"}, "App": {"Name": "AirMonitor", "Version": "1.0.0", "Environment": "Development", "DebugMode": true, "DataCollectionInterval": 5000, "BatchProcessingSize": 100, "MaxConcurrentDevices": 50, "DataRetentionDays": 365, "EnablePerformanceMonitoring": true, "EnableHealthChecks": true, "HealthCheckInterval": 30, "TempDirectory": "temp", "LogDirectory": "logs", "BackupDirectory": "backup"}, "Database": {"CommandTimeout": 30, "MaxPoolSize": 100, "MinPoolSize": 5, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "EnableAutoMigration": false, "BatchSize": 1000, "QueryTrackingBehavior": "TrackAll", "EnableSplitQueries": true, "RetryCount": 3, "RetryDelay": 1000}, "SerialPort": {"DefaultPortName": "COM1", "BaudRate": 9600, "DataBits": 8, "StopBits": "One", "Parity": "None", "ReadTimeout": 5000, "WriteTimeout": 5000, "ReceiveBufferSize": 4096, "SendBufferSize": 4096, "DtrEnable": false, "RtsEnable": false, "RetryCount": 3, "RetryInterval": 1000, "AutoReconnect": true, "HeartbeatInterval": 30000}, "Api": {"Version": "v1", "Title": "AirMonitor API", "Description": "商用空调监控系统 API", "Contact": {"Name": "AirMonitor Team", "Email": "<EMAIL>", "Url": "https://www.airmonitor.com"}, "License": {"Name": "MIT", "Url": "https://opensource.org/licenses/MIT"}, "EnableSwaggerUI": true, "EnableVersioning": true, "DefaultVersion": "1.0", "RateLimit": {"Enabled": true, "RequestsPerMinute": 100, "RequestsPerHour": 1000, "RequestsPerDay": 10000}, "Cors": {"Enabled": true, "AllowedOrigins": ["http://localhost:3000", "https://localhost:7000", "http://localhost:5000", "https://localhost:5001"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["Content-Type", "Authorization", "X-Correlation-ID", "X-Requested-With"], "AllowCredentials": false, "PreflightMaxAge": 86400}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "AirMonitor": "Debug"}}, "AllowedHosts": "*"}