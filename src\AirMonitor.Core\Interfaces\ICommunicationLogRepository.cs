using AirMonitor.Core.Entities.Monitoring;

namespace AirMonitor.Core.Interfaces;

/// <summary>
/// 通信日志仓储接口
/// </summary>
public interface ICommunicationLogRepository
{
    /// <summary>
    /// 保存通信日志
    /// </summary>
    /// <param name="log">通信日志</param>
    /// <returns>保存的通信日志</returns>
    Task<CommunicationLog> SaveAsync(CommunicationLog log);
    
    /// <summary>
    /// 获取设备通信日志
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="count">日志条数</param>
    /// <returns>通信日志集合</returns>
    Task<IEnumerable<CommunicationLog>> GetByDeviceAsync(int deviceId, int count = 100);
    
    /// <summary>
    /// 根据时间范围获取通信日志
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>通信日志集合</returns>
    Task<IEnumerable<CommunicationLog>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end);
    
    /// <summary>
    /// 获取最新通信日志
    /// </summary>
    /// <param name="count">日志条数</param>
    /// <returns>通信日志集合</returns>
    Task<IEnumerable<CommunicationLog>> GetLatestAsync(int count = 100);
}
