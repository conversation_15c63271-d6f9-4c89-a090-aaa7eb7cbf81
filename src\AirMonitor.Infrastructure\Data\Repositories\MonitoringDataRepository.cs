using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Monitoring;
using AirMonitor.Core.Interfaces;

namespace AirMonitor.Infrastructure.Data.Repositories;

/// <summary>
/// 监控数据仓储实现
/// </summary>
public class MonitoringDataRepository : IMonitoringDataRepository
{
    private readonly AirMonitorDbContext _context;

    public MonitoringDataRepository(AirMonitorDbContext context)
    {
        _context = context;
    }

    public async Task<MonitoringData> SaveAsync(MonitoringData data)
    {
        data.CreatedAt = DateTimeOffset.UtcNow;
        
        _context.MonitoringData.Add(data);
        await _context.SaveChangesAsync();
        return data;
    }

    public async Task<IEnumerable<MonitoringData>> GetLatestByDeviceAsync(int deviceId, int count = 100)
    {
        return await _context.MonitoringData
            .Where(m => m.DeviceId == deviceId)
            .OrderByDescending(m => m.Timestamp)
            .Take(count)
            .ToListAsync();
    }

    public async Task<IEnumerable<MonitoringData>> GetByTimeRangeAsync(int deviceId, DateTimeOffset start, DateTimeOffset end)
    {
        return await _context.MonitoringData
            .Where(m => m.DeviceId == deviceId && m.Timestamp >= start && m.Timestamp <= end)
            .OrderBy(m => m.Timestamp)
            .ToListAsync();
    }

    public async Task<decimal?> GetLatestNumericValueAsync(int deviceId, byte parameterIndex)
    {
        var latestData = await _context.MonitoringData
            .Where(m => m.DeviceId == deviceId && m.ParameterIndex == parameterIndex)
            .OrderByDescending(m => m.Timestamp)
            .FirstOrDefaultAsync();

        return latestData?.NumericValue;
    }

    public async Task<MonitoringData?> GetLatestByParameterAsync(int deviceId, byte parameterIndex)
    {
        return await _context.MonitoringData
            .Where(m => m.DeviceId == deviceId && m.ParameterIndex == parameterIndex)
            .OrderByDescending(m => m.Timestamp)
            .FirstOrDefaultAsync();
    }
}
