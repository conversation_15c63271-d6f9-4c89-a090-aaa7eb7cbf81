namespace AirMonitor.Core.Enums;

/// <summary>
/// 用户角色枚举
/// </summary>
public enum UserRole
{
    /// <summary>
    /// 系统管理员
    /// </summary>
    SystemAdmin = 1,
    
    /// <summary>
    /// 研发人员
    /// </summary>
    RDPersonnel = 2,
    
    /// <summary>
    /// 服务人员
    /// </summary>
    ServicePersonnel = 3,
    
    /// <summary>
    /// 普通用户
    /// </summary>
    RegularUser = 4
}

/// <summary>
/// 权限级别枚举
/// </summary>
public enum PermissionLevel
{
    /// <summary>
    /// 无权限
    /// </summary>
    None = 0,
    
    /// <summary>
    /// 只读
    /// </summary>
    ReadOnly = 1,
    
    /// <summary>
    /// 读写
    /// </summary>
    ReadWrite = 2,
    
    /// <summary>
    /// 完全控制
    /// </summary>
    FullControl = 3
}
