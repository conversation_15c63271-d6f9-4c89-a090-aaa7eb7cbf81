using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using AirMonitor.Core.Interfaces;
using AirMonitor.Infrastructure.Data;
using AirMonitor.Infrastructure.Data.Repositories;

namespace AirMonitor.Infrastructure;

/// <summary>
/// 基础设施层依赖注入扩展
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// 添加基础设施层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 添加数据库上下文
        services.AddDbContext<AirMonitorDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            if (string.IsNullOrEmpty(connectionString))
            {
                // 开发环境使用SQLite
                connectionString = "Data Source=AirMonitor.db";
                options.UseSqlite(connectionString);
            }
            else
            {
                // 生产环境使用SQL Server
                options.UseSqlServer(connectionString);
            }
            
            // 开发环境启用敏感数据日志
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }
        });

        // 注册仓储服务
        services.AddScoped<IDeviceRepository, DeviceRepository>();
        services.AddScoped<IMonitoringDataRepository, MonitoringDataRepository>();
        services.AddScoped<IParameterDefinitionRepository, ParameterDefinitionRepository>();
        services.AddScoped<IDeviceParameterRepository, DeviceParameterRepository>();
        services.AddScoped<ICommunicationLogRepository, CommunicationLogRepository>();

        return services;
    }

    /// <summary>
    /// 初始化数据库
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>任务</returns>
    public static async Task InitializeDatabaseAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AirMonitorDbContext>();
        
        // 应用数据库迁移
        await context.Database.MigrateAsync();
        
        // 初始化种子数据
        await Data.SeedData.DatabaseSeeder.SeedAsync(context);
    }
}
