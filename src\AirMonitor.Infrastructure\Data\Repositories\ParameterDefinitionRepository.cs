using Microsoft.EntityFrameworkCore;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Enums;
using AirMonitor.Core.Interfaces;

namespace AirMonitor.Infrastructure.Data.Repositories;

/// <summary>
/// 参数定义仓储实现
/// </summary>
public class ParameterDefinitionRepository : IParameterDefinitionRepository
{
    private readonly AirMonitorDbContext _context;

    public ParameterDefinitionRepository(AirMonitorDbContext context)
    {
        _context = context;
    }

    public async Task<ParameterDefinition?> GetByCommandAndIndexAsync(byte commandCode, byte parameterIndex)
    {
        return await _context.ParameterDefinitions
            .Include(p => p.ParameterTemplate)
            .FirstOrDefaultAsync(p => p.CommandCode == commandCode && p.ParameterIndex == parameterIndex && p.IsActive);
    }

    public async Task<ParameterDefinition?> GetDefinitionByIdAsync(int id)
    {
        return await _context.ParameterDefinitions
            .Include(p => p.ParameterTemplate)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<IEnumerable<ParameterDefinition>> GetByDeviceTypeAsync(DeviceType deviceType)
    {
        return await _context.ParameterDefinitions
            .Include(p => p.ParameterTemplate)
            .Where(p => p.ParameterTemplate.ApplicableDeviceType == deviceType && p.IsActive)
            .ToListAsync();
    }

    public async Task<ParameterDefinition?> GetByDeviceAndIndexAsync(int deviceId, byte parameterIndex)
    {
        // 首先获取设备信息
        var device = await _context.Devices.FirstOrDefaultAsync(d => d.Id == deviceId);
        if (device == null) return null;

        // 根据设备类型和型号查找匹配的参数定义
        return await _context.ParameterDefinitions
            .Include(p => p.ParameterTemplate)
            .Where(p => p.ParameterTemplate.ApplicableDeviceType == device.DeviceType &&
                       p.ParameterIndex == parameterIndex &&
                       p.IsActive &&
                       (p.ParameterTemplate.ModelPattern == "*" || device.ModelNumber.Contains(p.ParameterTemplate.ModelPattern)))
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<ParameterDefinition>> GetActiveDefinitionsAsync()
    {
        return await _context.ParameterDefinitions
            .Include(p => p.ParameterTemplate)
            .Where(p => p.IsActive)
            .ToListAsync();
    }
}
