using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using AirMonitor.Infrastructure.Configuration;
using AirMonitor.Infrastructure.DependencyInjection;

namespace AirMonitor.UnitTests;

public class InfrastructureTests
{
    [Fact]
    public void ConfigureOptions_ShouldBindConfigurationCorrectly()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["App:Name"] = "TestApp",
                ["App:Version"] = "1.0.0",
                ["App:DataCollectionInterval"] = "5000",
                ["SerialPort:DefaultPortName"] = "COM1",
                ["SerialPort:BaudRate"] = "9600",
                ["Database:ConnectionString"] = "Server=test;Database=test;",
                ["Database:CommandTimeout"] = "30",
                ["Api:Version"] = "v1",
                ["Api:Title"] = "Test API"
            })
            .Build();

        var services = new ServiceCollection();

        // Act
        services.ConfigureOptions(configuration);
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var appSettings = serviceProvider.GetRequiredService<IOptions<AppSettings>>().Value;
        Assert.Equal("TestApp", appSettings.Name);
        Assert.Equal("1.0.0", appSettings.Version);
        Assert.Equal(5000, appSettings.DataCollectionInterval);

        var serialPortSettings = serviceProvider.GetRequiredService<IOptions<SerialPortSettings>>().Value;
        Assert.Equal("COM1", serialPortSettings.DefaultPortName);
        Assert.Equal(9600, serialPortSettings.BaudRate);

        var databaseSettings = serviceProvider.GetRequiredService<IOptions<DatabaseSettings>>().Value;
        Assert.Equal(30, databaseSettings.CommandTimeout);

        var apiSettings = serviceProvider.GetRequiredService<IOptions<ApiSettings>>().Value;
        Assert.Equal("v1", apiSettings.Version);
        Assert.Equal("Test API", apiSettings.Title);
    }

    [Fact]
    public void AddAirMonitorInfrastructure_ShouldRegisterAllServices()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["App:Name"] = "TestApp",
                ["Database:ConnectionString"] = "Server=test;Database=test;",
                ["ConnectionStrings:DefaultConnection"] = "Server=test;Database=test;"
            })
            .Build();

        var services = new ServiceCollection();

        // Act
        services.AddAirMonitorInfrastructure(configuration);
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        Assert.NotNull(serviceProvider.GetService<IOptions<AppSettings>>());
        Assert.NotNull(serviceProvider.GetService<IOptions<SerialPortSettings>>());
        Assert.NotNull(serviceProvider.GetService<IOptions<DatabaseSettings>>());
        Assert.NotNull(serviceProvider.GetService<IOptions<ApiSettings>>());
    }
}