using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Monitoring;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 通信日志实体配置
/// </summary>
public class CommunicationLogConfiguration : IEntityTypeConfiguration<CommunicationLog>
{
    public void Configure(EntityTypeBuilder<CommunicationLog> builder)
    {
        builder.ToTable("CommunicationLogs");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.CommunicationType)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("通信类型");
            
        builder.Property(x => x.ProtocolFormat)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("协议格式");
            
        builder.Property(x => x.SourceAddress)
            .IsRequired()
            .HasComment("源地址");
            
        builder.Property(x => x.TargetAddress)
            .IsRequired()
            .HasComment("目标地址");
            
        builder.Property(x => x.CommandCode)
            .IsRequired()
            .HasComment("命令码");
            
        builder.Property(x => x.RawData)
            .IsRequired()
            .HasComment("原始数据");
            
        builder.Property(x => x.ParsedData)
            .HasComment("解析后的数据JSON");
            
        builder.Property(x => x.ErrorMessage)
            .HasMaxLength(1000)
            .HasComment("错误消息");
        
        // 索引
        builder.HasIndex(x => new { x.DeviceId, x.Timestamp })
            .HasDatabaseName("IX_CommunicationLogs_Device_Time");
            
        builder.HasIndex(x => x.Timestamp)
            .HasDatabaseName("IX_CommunicationLogs_Timestamp");
            
        builder.HasIndex(x => x.IsSuccessful)
            .HasDatabaseName("IX_CommunicationLogs_IsSuccessful");
            
        builder.HasIndex(x => x.CommandCode)
            .HasDatabaseName("IX_CommunicationLogs_CommandCode");
        
        // 关系配置
        builder.HasOne(x => x.Device)
            .WithMany(x => x.CommunicationLogs)
            .HasForeignKey(x => x.DeviceId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
