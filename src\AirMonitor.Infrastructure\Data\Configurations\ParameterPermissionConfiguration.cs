using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Parameters;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 参数权限实体配置
/// </summary>
public class ParameterPermissionConfiguration : IEntityTypeConfiguration<ParameterPermission>
{
    public void Configure(EntityTypeBuilder<ParameterPermission> builder)
    {
        builder.ToTable("ParameterPermissions");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.UserRole)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("用户角色");
            
        builder.Property(x => x.PermissionLevel)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("权限级别");
        
        // 索引
        builder.HasIndex(x => new { x.UserRole, x.ParameterDefinitionId })
            .IsUnique()
            .HasDatabaseName("IX_ParameterPermissions_Role_Parameter");
            
        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("IX_ParameterPermissions_IsActive");
        
        // 关系配置
        builder.HasOne(x => x.ParameterDefinition)
            .WithMany()
            .HasForeignKey(x => x.ParameterDefinitionId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
