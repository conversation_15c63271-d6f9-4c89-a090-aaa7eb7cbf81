# 基础架构搭建 开发文档

## 1. 功能需求描述

### 1.1 业务背景
基础架构是整个 AirMonitor 系统的技术支撑层，为所有业务模块提供统一的配置管理、依赖注入、日志记录、异常处理等基础服务。确保系统的可维护性、可扩展性和稳定性。

### 1.2 功能范围
- **配置管理系统**：统一的配置文件管理和环境变量支持
- **依赖注入容器**：服务注册和生命周期管理
- **日志记录框架**：结构化日志记录和输出管理
- **异常处理机制**：全局异常捕获和标准化错误响应
- **中间件管道**：请求处理中间件配置
- **跨域配置**：CORS 策略设置
- **健康检查**：系统健康状态监控
- **API 版本控制**：接口版本管理

### 1.3 用户故事
- 作为开发人员，我希望有统一的配置管理，以便在不同环境中灵活部署
- 作为系统管理员，我希望有完整的日志记录，以便监控和排查问题
- 作为运维人员，我希望有健康检查机制，以便监控系统状态

## 2. 技术实现方案

### 2.1 架构设计
```
基础架构层 (Infrastructure)
├── Configuration/          # 配置管理
│   ├── AppSettings.cs     # 应用配置模型
│   ├── DatabaseSettings.cs # 数据库配置
│   └── SerialPortSettings.cs # 串口配置
├── DependencyInjection/   # 依赖注入
│   ├── ServiceCollectionExtensions.cs
│   └── ServiceRegistration.cs
├── Logging/               # 日志记录
│   ├── LoggingExtensions.cs
│   └── SerilogConfiguration.cs
├── Middleware/            # 中间件
│   ├── ExceptionHandlingMiddleware.cs
│   ├── RequestLoggingMiddleware.cs
│   └── CorrelationIdMiddleware.cs
└── HealthChecks/          # 健康检查
    ├── DatabaseHealthCheck.cs
    └── SerialPortHealthCheck.cs
```

### 2.2 技术选型
- **配置管理**：IConfiguration + appsettings.json
- **依赖注入**：Microsoft.Extensions.DependencyInjection
- **日志框架**：Serilog + Serilog.AspNetCore
- **健康检查**：Microsoft.Extensions.Diagnostics.HealthChecks
- **API 文档**：Swagger/OpenAPI
- **跨域处理**：Microsoft.AspNetCore.Cors

### 2.3 设计模式
- **选项模式 (Options Pattern)**：强类型配置管理
- **扩展方法模式**：服务注册的链式调用
- **中间件模式**：请求处理管道
- **工厂模式**：服务实例创建

## 3. 配置管理设计

### 3.1 配置文件结构
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=AirMonitor;Trusted_Connection=true;",
    "Redis": "localhost:6379"
  },
  "SerialPort": {
    "DefaultPortName": "COM1",
    "BaudRate": 9600,
    "DataBits": 8,
    "StopBits": "One",
    "Parity": "None",
    "ReadTimeout": 5000,
    "WriteTimeout": 5000
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    },
    "Serilog": {
      "MinimumLevel": "Information",
      "WriteTo": [
        {
          "Name": "Console"
        },
        {
          "Name": "File",
          "Args": {
            "path": "logs/airmonitor-.log",
            "rollingInterval": "Day"
          }
        }
      ]
    }
  },
  "Api": {
    "Version": "v1",
    "Title": "AirMonitor API",
    "Description": "商用空调监控系统 API"
  },
  "Cors": {
    "AllowedOrigins": ["http://localhost:3000", "https://localhost:7000"],
    "AllowedMethods": ["GET", "POST", "PUT", "DELETE"],
    "AllowedHeaders": ["*"]
  }
}
```

### 3.2 强类型配置类
```csharp
public class SerialPortSettings
{
    public const string SectionName = "SerialPort";
    
    public string DefaultPortName { get; set; } = "COM1";
    public int BaudRate { get; set; } = 9600;
    public int DataBits { get; set; } = 8;
    public StopBits StopBits { get; set; } = StopBits.One;
    public Parity Parity { get; set; } = Parity.None;
    public int ReadTimeout { get; set; } = 5000;
    public int WriteTimeout { get; set; } = 5000;
}
```

## 4. 依赖注入设计

### 4.1 服务注册策略
```csharp
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAirMonitorServices(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // 配置选项
        services.ConfigureOptions(configuration);
        
        // 基础设施服务
        services.AddInfrastructureServices();
        
        // 业务服务
        services.AddBusinessServices();
        
        // 数据访问服务
        services.AddDataAccessServices(configuration);
        
        return services;
    }
}
```

### 4.2 服务生命周期管理
- **Singleton**：配置服务、日志服务
- **Scoped**：数据库上下文、业务服务
- **Transient**：轻量级服务、工具类

## 5. 日志记录设计

### 5.1 Serilog 配置
```csharp
public static class SerilogConfiguration
{
    public static void ConfigureSerilog(this WebApplicationBuilder builder)
    {
        builder.Host.UseSerilog((context, configuration) =>
        {
            configuration
                .ReadFrom.Configuration(context.Configuration)
                .Enrich.FromLogContext()
                .Enrich.WithMachineName()
                .Enrich.WithEnvironmentUserName()
                .WriteTo.Console()
                .WriteTo.File("logs/airmonitor-.log", 
                    rollingInterval: RollingInterval.Day);
        });
    }
}
```

### 5.2 结构化日志标准
```csharp
// 操作日志
_logger.LogInformation("设备 {DeviceId} 连接成功，端口：{PortName}", 
    deviceId, portName);

// 错误日志
_logger.LogError(ex, "串口通信失败，设备：{DeviceId}，错误：{ErrorMessage}", 
    deviceId, ex.Message);

// 性能日志
using (_logger.BeginScope("数据采集操作 {OperationId}", operationId))
{
    _logger.LogInformation("开始数据采集，设备数量：{DeviceCount}", deviceCount);
    // 业务逻辑
    _logger.LogInformation("数据采集完成，耗时：{ElapsedMs}ms", elapsed);
}
```

## 6. 异常处理设计

### 6.1 全局异常处理中间件
```csharp
public class ExceptionHandlingMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }
    
    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = exception switch
        {
            ValidationException => CreateResponse(400, "验证失败", exception.Message),
            NotFoundException => CreateResponse(404, "资源未找到", exception.Message),
            UnauthorizedException => CreateResponse(401, "未授权访问", exception.Message),
            _ => CreateResponse(500, "服务器内部错误", "系统发生未知错误")
        };
        
        context.Response.StatusCode = response.StatusCode;
        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }
}
```

## 7. 健康检查设计

### 7.1 健康检查配置
```csharp
services.AddHealthChecks()
    .AddSqlServer(connectionString, name: "database")
    .AddRedis(redisConnectionString, name: "redis")
    .AddCheck<SerialPortHealthCheck>("serialport")
    .AddCheck<ApiHealthCheck>("api");
```

### 7.2 自定义健康检查
```csharp
public class SerialPortHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查串口可用性
            var availablePorts = SerialPort.GetPortNames();
            
            return availablePorts.Length > 0 
                ? HealthCheckResult.Healthy($"发现 {availablePorts.Length} 个可用串口")
                : HealthCheckResult.Degraded("未发现可用串口");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("串口检查失败", ex);
        }
    }
}
```

## 8. API 版本控制设计

### 8.1 版本控制策略
- **URL 路径版本控制**：`/api/v1/devices`
- **请求头版本控制**：`X-Version: 1.0`
- **查询参数版本控制**：`?version=1.0`

### 8.2 版本控制配置
```csharp
services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = new ApiVersion(1, 0);
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ApiVersionReader = ApiVersionReader.Combine(
        new UrlSegmentApiVersionReader(),
        new HeaderApiVersionReader("X-Version"),
        new QueryStringApiVersionReader("version")
    );
});
```

## 9. 单元测试计划

### 9.1 测试策略
- **配置管理测试**：验证配置绑定和验证逻辑
- **依赖注入测试**：验证服务注册和解析
- **中间件测试**：验证异常处理和日志记录
- **健康检查测试**：验证各项健康检查逻辑

### 9.2 测试用例设计
```csharp
[Fact]
public void ConfigureServices_ShouldRegisterAllRequiredServices()
{
    // Arrange
    var services = new ServiceCollection();
    var configuration = CreateTestConfiguration();
    
    // Act
    services.AddAirMonitorServices(configuration);
    var serviceProvider = services.BuildServiceProvider();
    
    // Assert
    Assert.NotNull(serviceProvider.GetService<ILogger<object>>());
    Assert.NotNull(serviceProvider.GetService<IConfiguration>());
}
```

## 10. 性能与安全考虑

### 10.1 性能要求
- **启动时间**：应用启动时间 < 10秒
- **内存使用**：基础架构内存占用 < 100MB
- **日志性能**：日志写入不影响主业务性能

### 10.2 安全措施
- **配置安全**：敏感配置使用环境变量或密钥管理
- **日志安全**：避免记录敏感信息
- **异常安全**：生产环境不暴露详细错误信息

### 10.3 监控指标
- **应用性能**：响应时间、吞吐量、错误率
- **资源使用**：CPU、内存、磁盘使用率
- **业务指标**：设备连接数、数据处理量
