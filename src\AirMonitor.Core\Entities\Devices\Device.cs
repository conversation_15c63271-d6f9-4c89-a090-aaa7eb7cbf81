using AirMonitor.Core.Enums;
using AirMonitor.Core.Entities.Parameters;
using AirMonitor.Core.Entities.Monitoring;
using AirMonitor.Core.Entities.Users;

namespace AirMonitor.Core.Entities.Devices;

/// <summary>
/// RS485设备实体
/// </summary>
public class Device
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// RS485设备地址
    /// </summary>
    public byte DeviceAddress { get; set; }
    
    /// <summary>
    /// 设备类型
    /// </summary>
    public DeviceType DeviceType { get; set; }
    
    /// <summary>
    /// 设备序列号
    /// </summary>
    public string? SerialNumber { get; set; }
    
    /// <summary>
    /// 设备型号
    /// </summary>
    public string ModelNumber { get; set; } = string.Empty;
    
    /// <summary>
    /// 固件版本
    /// </summary>
    public string FirmwareVersion { get; set; } = string.Empty;
    
    /// <summary>
    /// 设备状态
    /// </summary>
    public DeviceStatus Status { get; set; }
    
    /// <summary>
    /// 最后通信时间
    /// </summary>
    public DateTimeOffset LastCommunication { get; set; }
    
    /// <summary>
    /// 是否在线
    /// </summary>
    public bool IsOnline { get; set; }
    
    /// <summary>
    /// 设备位置
    /// </summary>
    public string? Location { get; set; }
    
    /// <summary>
    /// 设备描述
    /// </summary>
    public string? Description { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTimeOffset UpdatedAt { get; set; }
    
    // 导航属性
    
    /// <summary>
    /// 设备参数集合
    /// </summary>
    public virtual ICollection<DeviceParameter> DeviceParameters { get; set; } = new List<DeviceParameter>();
    
    /// <summary>
    /// 通信日志集合
    /// </summary>
    public virtual ICollection<CommunicationLog> CommunicationLogs { get; set; } = new List<CommunicationLog>();
    
    /// <summary>
    /// 监控数据集合
    /// </summary>
    public virtual ICollection<MonitoringData> MonitoringData { get; set; } = new List<MonitoringData>();
    
    /// <summary>
    /// 用户权限集合
    /// </summary>
    public virtual ICollection<UserDevicePermission> UserPermissions { get; set; } = new List<UserDevicePermission>();
}
