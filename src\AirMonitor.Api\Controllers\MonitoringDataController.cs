using Microsoft.AspNetCore.Mvc;
using AirMonitor.Core.Interfaces;
using AirMonitor.Core.Entities.Monitoring;

namespace AirMonitor.Api.Controllers;

/// <summary>
/// 监控数据控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class MonitoringDataController : ControllerBase
{
    private readonly IMonitoringDataRepository _monitoringDataRepository;
    private readonly IDeviceRepository _deviceRepository;
    private readonly ILogger<MonitoringDataController> _logger;

    public MonitoringDataController(
        IMonitoringDataRepository monitoringDataRepository,
        IDeviceRepository deviceRepository,
        ILogger<MonitoringDataController> logger)
    {
        _monitoringDataRepository = monitoringDataRepository;
        _deviceRepository = deviceRepository;
        _logger = logger;
    }

    /// <summary>
    /// 获取设备最新监控数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="count">数据条数</param>
    /// <returns>监控数据列表</returns>
    [HttpGet("device/{deviceId}/latest")]
    public async Task<ActionResult<IEnumerable<MonitoringData>>> GetLatestByDevice(int deviceId, [FromQuery] int count = 100)
    {
        try
        {
            // 验证设备是否存在
            var device = await _deviceRepository.GetByIdAsync(deviceId);
            if (device == null)
            {
                return NotFound($"设备 ID {deviceId} 不存在");
            }

            var data = await _monitoringDataRepository.GetLatestByDeviceAsync(deviceId, count);
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 最新监控数据失败", deviceId);
            return StatusCode(500, "获取监控数据失败");
        }
    }

    /// <summary>
    /// 根据时间范围获取监控数据
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>监控数据列表</returns>
    [HttpGet("device/{deviceId}/range")]
    public async Task<ActionResult<IEnumerable<MonitoringData>>> GetByTimeRange(
        int deviceId,
        [FromQuery] DateTimeOffset start,
        [FromQuery] DateTimeOffset end)
    {
        try
        {
            // 验证时间范围
            if (start >= end)
            {
                return BadRequest("开始时间必须小于结束时间");
            }

            // 验证设备是否存在
            var device = await _deviceRepository.GetByIdAsync(deviceId);
            if (device == null)
            {
                return NotFound($"设备 ID {deviceId} 不存在");
            }

            var data = await _monitoringDataRepository.GetByTimeRangeAsync(deviceId, start, end);
            return Ok(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 时间范围监控数据失败", deviceId);
            return StatusCode(500, "获取监控数据失败");
        }
    }

    /// <summary>
    /// 获取参数最新数值
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="parameterIndex">参数索引</param>
    /// <returns>最新数值</returns>
    [HttpGet("device/{deviceId}/parameter/{parameterIndex}/latest")]
    public async Task<ActionResult<object>> GetLatestParameterValue(int deviceId, byte parameterIndex)
    {
        try
        {
            // 验证设备是否存在
            var device = await _deviceRepository.GetByIdAsync(deviceId);
            if (device == null)
            {
                return NotFound($"设备 ID {deviceId} 不存在");
            }

            var latestData = await _monitoringDataRepository.GetLatestByParameterAsync(deviceId, parameterIndex);
            if (latestData == null)
            {
                return NotFound($"设备 {deviceId} 参数 {parameterIndex:X2} 无监控数据");
            }

            return Ok(new
            {
                deviceId = latestData.DeviceId,
                parameterIndex = latestData.ParameterIndex,
                rawValue = latestData.RawValue,
                parsedValue = latestData.ParsedValue,
                numericValue = latestData.NumericValue,
                stringValue = latestData.StringValue,
                booleanValue = latestData.BooleanValue,
                quality = latestData.Quality,
                timestamp = latestData.Timestamp
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 参数 {ParameterIndex} 最新数值失败", deviceId, parameterIndex);
            return StatusCode(500, "获取参数数值失败");
        }
    }

    /// <summary>
    /// 保存监控数据
    /// </summary>
    /// <param name="data">监控数据</param>
    /// <returns>保存的监控数据</returns>
    [HttpPost]
    public async Task<ActionResult<MonitoringData>> SaveMonitoringData(MonitoringData data)
    {
        try
        {
            // 验证设备是否存在
            var device = await _deviceRepository.GetByIdAsync(data.DeviceId);
            if (device == null)
            {
                return NotFound($"设备 ID {data.DeviceId} 不存在");
            }

            var savedData = await _monitoringDataRepository.SaveAsync(data);
            return CreatedAtAction(nameof(GetLatestParameterValue), 
                new { deviceId = savedData.DeviceId, parameterIndex = savedData.ParameterIndex }, 
                savedData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存监控数据失败");
            return StatusCode(500, "保存监控数据失败");
        }
    }
}
