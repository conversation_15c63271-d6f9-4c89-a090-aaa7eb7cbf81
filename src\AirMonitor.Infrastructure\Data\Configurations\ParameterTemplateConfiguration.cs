using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using AirMonitor.Core.Entities.Parameters;

namespace AirMonitor.Infrastructure.Data.Configurations;

/// <summary>
/// 参数模板实体配置
/// </summary>
public class ParameterTemplateConfiguration : IEntityTypeConfiguration<ParameterTemplate>
{
    public void Configure(EntityTypeBuilder<ParameterTemplate> builder)
    {
        builder.ToTable("ParameterTemplates");
        
        builder.HasKey(x => x.Id);
        
        // 属性配置
        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("模板名称");
            
        builder.Property(x => x.Description)
            .HasMaxLength(500)
            .HasComment("模板描述");
            
        builder.Property(x => x.ApplicableDeviceType)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("适用的设备类型");
            
        builder.Property(x => x.ModelPattern)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("设备型号匹配模式");
        
        // 索引
        builder.HasIndex(x => x.Name)
            .HasDatabaseName("IX_ParameterTemplates_Name");
            
        builder.HasIndex(x => new { x.ApplicableDeviceType, x.ModelPattern })
            .HasDatabaseName("IX_ParameterTemplates_DeviceType_ModelPattern");
        
        // 关系配置
        builder.HasMany(x => x.ParameterDefinitions)
            .WithOne(x => x.ParameterTemplate)
            .HasForeignKey(x => x.ParameterTemplateId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
